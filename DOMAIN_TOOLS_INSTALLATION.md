# Domain Evaluation Tools Installation Guide

## Overview
This guide explains how to install and activate the new domain evaluation tools that have been added to your DomainsKit website.

## New Tools Added

1. **Geo Domain Checker** - Evaluates geographic domain value
2. **Exact Match Checker** - Analyzes exact match keywords
3. **Brandable Checker** - Evaluates brandability potential
4. **Keyword Checker** - Analyzes keyword potential and search volume

## Installation Steps

### Step 1: Database Setup
You need to add the new tool settings to your database. Choose one of these methods:

#### Method A: Run SQL Script (Recommended)
1. Open your database management tool (phpMyAdmin, etc.)
2. Select your DomainsKit database
3. Run the SQL script from `add_domain_tools_options.sql`

#### Method B: Run Migration (Advanced)
1. Visit: `your-domain.com/admin/migrate`
2. This will automatically run all pending migrations

### Step 2: Clear Cache
1. Delete the file: `application/cache/options` (if it exists)
2. Or visit your admin panel and the cache will be automatically cleared

### Step 3: Verify Installation
1. Go to Admin Panel → Tools Settings
2. You should now see the new tools:
   - Enable Geo Domain Checker Tool
   - Enable Exact Match Checker Tool
   - Enable Brandable Checker Tool
   - Enable Keyword Checker Tool

### Step 4: Configure SEO Settings (Optional)
1. Go to Admin Panel → Options → SEO Settings
2. Configure the SEO titles, keywords, and descriptions for each new tool

## Tool URLs

Once activated, the tools will be available at:
- `/geo-domain-checker` - Geographic Domain Evaluation
- `/exact-match-checker` - Exact Match Keywords Analysis
- `/brandable-checker` - Brandability Assessment
- `/keyword-checker` - Keyword Potential Analysis

## Features

### Geo Domain Checker
- Detects city names and geographic locations in domains
- Evaluates commercial potential based on location importance
- Provides market value estimates
- Supports international cities

### Exact Match Checker
- Identifies high-value commercial keywords
- Analyzes SEO and PPC potential
- Calculates keyword density
- Provides commercial value scoring

### Brandable Checker
- Evaluates memorability and pronounceability
- Assesses uniqueness and creativity
- Measures industry versatility
- Provides branding recommendations

### Keyword Checker
- Analyzes search volume potential
- Evaluates competition levels
- Supports multiple languages (English/Arabic)
- Provides SEO/PPC recommendations

## Scoring System

All tools use a comprehensive 5-factor scoring system:
- **Geographic Score**: 20 points maximum
- **Commercial Value**: 25 points maximum
- **Market Demand**: 20 points maximum
- **Competition Level**: 15 points maximum
- **Brandability**: 20 points maximum

**Total Maximum Score**: 100 points

## Troubleshooting

### Tools Not Showing in Admin Panel
1. Ensure database options were added correctly
2. Clear the cache file: `application/cache/options`
3. Check that all files were uploaded correctly

### Tools Not Working
1. Verify all controller files are in `application/controllers/`
2. Check that view files are in `application/views/themes/default/pages/`
3. Ensure language files are updated

### 404 Errors
1. Check that routes are properly configured in `application/config/routes.php`
2. Verify .htaccess file is working correctly

## Support

If you encounter any issues:
1. Check the error logs in `application/logs/`
2. Verify all files were uploaded to correct locations
3. Ensure database permissions are correct

## File Structure

```
application/
├── controllers/
│   ├── Geo_domain_checker.php
│   ├── Exact_match_checker.php
│   ├── Brandable_checker.php
│   └── Keyword_checker.php
├── views/themes/default/pages/
│   ├── geo_domain_checker.php
│   ├── exact_match_checker.php
│   ├── brandable_checker.php
│   └── keyword_checker.php
├── language/main/
│   ├── tools_lang.php (updated)
│   └── global_lang.php (updated)
└── config/
    ├── options.php (updated)
    └── routes.php (updated)
```

## Customization

You can customize the tools by:
1. Modifying the scoring algorithms in the controllers
2. Updating the UI/UX in the view files
3. Adding new evaluation criteria
4. Integrating with external APIs for enhanced data

Enjoy your new domain evaluation tools!
