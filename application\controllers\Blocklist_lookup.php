<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Blocklist_lookup extends FrontController {

	public function __construct() {
        parent::__construct();
		if (!$this->options->get('blocklist-status')) {
			if($this->options->get('search-status'))
			return redirect(base_url());
			else if($this->options->get('generator-status'))
			return redirect(base_url('domain-generator'));
			else if($this->options->get('whois-status'))
			return redirect(base_url('whois'));
			else if($this->options->get('ip-status'))
			return redirect(base_url('ip-lookup'));
			else if($this->options->get('location-status'))
			return redirect(base_url('location'));
			else if($this->options->get('dns-status'))
			return redirect(base_url('dns-lookup'));
			else if($this->options->get('open-ports-status'))
			return redirect(base_url('open-ports-lookup'));
			else {
				$this->output->set_status_header('404');
				return redirect(base_url('404'));
			}
		}
        $this->load->model('Modules/TldsModel');
    }

	public function index() {
        $this->lang->load('tools', 'main');
		$this->theme->view('pages/blocklist', [
		 	'title'       => $this->options->get('seo-blocklist-title'),
		 	'description' => $this->options->get('seo-blocklist-description'),
			'keywords' => $this->options->get('seo-blocklist-keywords'),
			'canonical' => base_url(),
			'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
			'scripts' => [$this->theme->url('assets/js/jquery.min.js'), $this->theme->url('assets/js/components/blocklist.js')]
		]);
	}

	public function set_mode() {
		if(get_mode() == 'light')
		set_mode('dark');
		else
		set_mode('light');
		!empty($this->input->get('redirect_url')) ? redirect($this->input->get('redirect_url')) : redirect(base_url());
		exit();
	}

	public function query() {
		$this->lang->load('tools', 'main');
		$url = trim($this->input->post('url'));
		if(!is_valid_ip_address($url))
		$url = gethostbyname($url);
		if(is_valid_ip_address($url)) {
			$response['domain'] = $url;
			$response['status'] = 'blank';
			$response['other_tlds'] = array(
				"all.s5h.net",
				"b.barracudacentral.org",
				"bl.spamcop.net",
				"blacklist.woody.ch",
				"bogons.cymru.com",
				"cbl.abuseat.org",
				"combined.abuse.ch",
				"db.wpbl.info",
				"dnsbl-1.uceprotect.net",
				"dnsbl-2.uceprotect.net",
				"dnsbl-3.uceprotect.net",
				"dnsbl.dronebl.org",
				"dnsbl.sorbs.net",
				"drone.abuse.ch",
				"duinv.aupads.org",
				"dul.dnsbl.sorbs.net",
				"dyna.spamrats.com",
				"http.dnsbl.sorbs.net",
				"ips.backscatterer.org",
				"ix.dnsbl.manitu.net",
				"korea.services.net",
				"misc.dnsbl.sorbs.net",
				"noptr.spamrats.com",
				"orvedb.aupads.org",
				"pbl.spamhaus.org",
				"proxy.bl.gweep.ca",
				"psbl.surriel.com",
				"relays.bl.gweep.ca",
				"relays.nether.net",
				"sbl.spamhaus.org",
				"singular.ttk.pte.hu",
				"smtp.dnsbl.sorbs.net",
				"socks.dnsbl.sorbs.net",
				"spam.abuse.ch",
				"spam.dnsbl.anonmails.de",
				"spam.dnsbl.sorbs.net",
				"spam.spamrats.com",
				"spambot.bls.digibase.ca",
				"spamrbl.imp.ch",
				"spamsources.fabel.dk",
				"ubl.lashback.com",
				"ubl.unsubscore.com",
				"virus.rbl.jp",
				"web.dnsbl.sorbs.net",
				"wormrbl.imp.ch",
				"xbl.spamhaus.org",
				"z.mailspike.net",
				"zen.spamhaus.org",
				"zombie.dnsbl.sorbs.net",
				"rbl.your-server.de"
			);
		} else {
			$response = array(
				"type" => "error",
				"message" => lang('errors_invalid_url_ip')
			);
		}
        echo json_encode($response);
	}

	public function single_query() {
		$response = array(
            "status" => "blank",
            "link" => "#",
        );
		$url = "";
		$domain = "";
		if($this->input->post('url')) {
			$url = $this->input->post('url');
			$domain = $this->input->post('domain');
		}
		if($this->query_blocklist($url, $domain))
		$response['status'] = "blocked";
		else
		$response['status'] = "clear";
		$response['link']   = $url;
		$response['domain']   = $domain;
		echo json_encode($response);
	}

	public function query_blocklist($host, $ip) {
		$reverseIp = implode('.', array_reverse(explode('.', $ip)));
    	if (checkdnsrr("$reverseIp.$host.", "A"))
		return true;
		return false;
    }
}