<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Blog extends FrontController {

	public function __construct() {
        parent::__construct();
        $this->load->model('Modules/BlogModel');
		$this->load->library("pagination");
		if(!$this->options->get('blog-status')) {
			$this->output->set_status_header('404');
			redirect(base_url('404'));
		}
    }

	public function index($page = 1) {
		if(is_numeric($page) && $page > 0) {
			//$posts = $this->BlogModel->get($page);
			$config["base_url"] = base_url() . "blog";
			$config["total_rows"] = $this->BlogModel->get_count_all();
			$config["per_page"] = (int)9;
			$config["uri_segment"] = 2;
			/*
			  start 
			  add boostrap class and styles
			*/
			$config['full_tag_open'] = '<ul class="pagination">';        
			$config['full_tag_close'] = '</ul>';        
			$config['first_link'] = 'First';        
			$config['last_link'] = 'Last';        
			$config['first_tag_open'] = '<li class="page-item"><span class="page-link">';        
			$config['first_tag_close'] = '</span></li>';        
			$config['prev_link'] = '&laquo';        
			$config['prev_tag_open'] = '<li class="page-item"><span class="page-link">';        
			$config['prev_tag_close'] = '</span></li>';        
			$config['next_link'] = '&raquo';        
			$config['next_tag_open'] = '<li class="page-item"><span class="page-link">';        
			$config['next_tag_close'] = '</span></li>';        
			$config['last_tag_open'] = '<li class="page-item"><span class="page-link">';        
			$config['last_tag_close'] = '</span></li>';        
			$config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';        
			$config['cur_tag_close'] = '</a></li>';        
			$config['num_tag_open'] = '<li class="page-item"><span class="page-link">';        
			$config['num_tag_close'] = '</span></li>';
			/*
			  end 
			  add boostrap class and styles
			*/
			$this->pagination->initialize($config);
			$page = ($this->uri->segment(2)) ? $this->uri->segment(2) : 0;
		}
		$this->theme->view('pages/blog', [
				'title'       => $this->options->get('blog-title'),
				'description' => $this->options->get('blog-description'),
				'keywords' => $this->options->get('seo-blog-keywords'),
				'canonical' => base_url('blog' . ($page > 1 ? '/' . $page : '')),
				'posts' => $this->BlogModel->get_posts($config["per_page"], $page),
				'page' => $page,
				'links' => $this->pagination->create_links(),
				
		]);
	}

	public function post($slug = null) {
		$slug  = convert_to_utf8_slug($slug);
		if($post = $this->BlogModel->getBySlug($slug)) {
			$this->theme->view('pages/blog_single', [
				'title' => $post['title'],
				'description' => $post['post_description'],
				'canonical' => base_url('blog/post/' . $post['permalink']),
				'keywords' => $post['post_keywords'],
				'post' => $post
			]);
		} else {
			$this->output->set_status_header('404');
			redirect(base_url('404'));
		}
	}
}
