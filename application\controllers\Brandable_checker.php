<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Brandable_checker extends FrontController {
    
    // Brandable patterns and scoring
    private $brandable_patterns = [
        'tech_suffixes' => ['ly', 'fy', 'io', 'ai', 'co', 'app', 'lab', 'hub', 'box', 'kit'],
        'creative_suffixes' => ['ify', 'ize', 'able', 'ful', 'less', 'wise', 'like'],
        'modern_prefixes' => ['e', 'i', 'my', 'get', 'go', 'pro', 'max', 'super', 'ultra', 'mega'],
        'vowel_patterns' => ['a', 'e', 'i', 'o', 'u'],
        'consonant_clusters' => ['bl', 'br', 'cl', 'cr', 'dr', 'fl', 'fr', 'gl', 'gr', 'pl', 'pr', 'sl', 'sm', 'sn', 'sp', 'st', 'sw', 'tr']
    ];

    private $negative_patterns = [
        'hyphens' => ['-'],
        'numbers' => ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
        'underscores' => ['_'],
        'difficult_combinations' => ['xz', 'qx', 'zx', 'qq', 'xx', 'zz']
    ];

    public function __construct() {
        parent::__construct();
        if (!$this->options->get('brandable-status')) {
            if($this->options->get('search-status'))
                return redirect(base_url());
            else if($this->options->get('generator-status'))
                return redirect(base_url('domain-generator'));
            else if($this->options->get('whois-status'))
                return redirect(base_url('whois'));
            else if($this->options->get('ip-status'))
                return redirect(base_url('ip-lookup'));
            else if($this->options->get('location-status'))
                return redirect(base_url('location'));
            else if($this->options->get('dns-status'))
                return redirect(base_url('dns-lookup'));
            else if($this->options->get('blocklist-status'))
                return redirect(base_url('blocklist-lookup'));
            else if($this->options->get('open-ports-status'))
                return redirect(base_url('open-ports-lookup'));
            else {
                $this->output->set_status_header('404');
                return redirect(base_url('404'));
            }
        }
        $this->load->model('Modules/TldsModel');
    }

    public function index() {
        $this->lang->load('tools', 'main');
        $this->theme->view('pages/brandable_checker', [
            'title'       => $this->options->get('seo-brandable-title'),
            'description' => $this->options->get('seo-brandable-description'),
            'keywords'    => $this->options->get('seo-brandable-keywords'),
            'canonical'   => base_url('brandable-checker'),
            'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
            'scripts' => [$this->theme->url('assets/js/components/brandable_checker.js')]
        ]);
    }

    public function query() {
        $this->lang->load('tools', 'main');
        $response = array(
            "type" => "error",
            "message" => lang('errors_invalid_url_unknown')
        );

        $domain = trim($this->input->post('domain'));
        if (!$domain) {
            echo json_encode($response);
            return;
        }

        // Clean domain name
        $clean_domain = strtolower($domain);
        $clean_domain = preg_replace('/\.(com|net|org|co\.uk|ca|ae|sa)$/', '', $clean_domain);

        $evaluation = $this->evaluate_brandability($clean_domain);
        
        $response = array(
            "type" => "success",
            "domain" => $domain,
            "clean_domain" => $clean_domain,
            "memorability_score" => $evaluation['memorability_score'],
            "pronounceability_score" => $evaluation['pronounceability_score'],
            "uniqueness_score" => $evaluation['uniqueness_score'],
            "versatility_score" => $evaluation['versatility_score'],
            "total_score" => $evaluation['total_score'],
            "grade" => $evaluation['grade'],
            "estimated_value" => $evaluation['estimated_value'],
            "brand_potential" => $evaluation['brand_potential'],
            "industry_fit" => $evaluation['industry_fit'],
            "recommendations" => $evaluation['recommendations']
        );

        echo json_encode($response);
    }

    private function evaluate_brandability($domain) {
        $memorability_score = $this->calculate_memorability($domain);
        $pronounceability_score = $this->calculate_pronounceability($domain);
        $uniqueness_score = $this->calculate_uniqueness($domain);
        $versatility_score = $this->calculate_versatility($domain);
        
        $total_score = ($memorability_score + $pronounceability_score + $uniqueness_score + $versatility_score) / 4;
        $grade = $this->get_grade($total_score);
        $estimated_value = $this->get_estimated_value($total_score);
        $brand_potential = $this->assess_brand_potential($domain, $total_score);
        $industry_fit = $this->assess_industry_fit($domain);
        $recommendations = $this->get_recommendations($domain, $memorability_score, $pronounceability_score, $uniqueness_score, $versatility_score);

        return [
            'memorability_score' => $memorability_score,
            'pronounceability_score' => $pronounceability_score,
            'uniqueness_score' => $uniqueness_score,
            'versatility_score' => $versatility_score,
            'total_score' => $total_score,
            'grade' => $grade,
            'estimated_value' => $estimated_value,
            'brand_potential' => $brand_potential,
            'industry_fit' => $industry_fit,
            'recommendations' => $recommendations
        ];
    }

    private function calculate_memorability($domain) {
        $score = 50; // Base score
        
        // Length scoring (6-12 characters is optimal)
        $length = strlen($domain);
        if ($length >= 6 && $length <= 12) {
            $score += 20;
        } else if ($length >= 4 && $length <= 15) {
            $score += 10;
        } else if ($length > 20) {
            $score -= 20;
        }
        
        // Vowel-consonant balance
        $vowels = preg_match_all('/[aeiou]/', $domain);
        $consonants = preg_match_all('/[bcdfghjklmnpqrstvwxyz]/', $domain);
        $ratio = $consonants > 0 ? $vowels / $consonants : 0;
        if ($ratio >= 0.3 && $ratio <= 0.7) {
            $score += 15;
        }
        
        // Avoid repetitive patterns
        if (!preg_match('/(.)\1{2,}/', $domain)) {
            $score += 10;
        }
        
        // Penalize numbers and special characters
        if (preg_match('/[0-9\-_]/', $domain)) {
            $score -= 15;
        }
        
        return min(max($score, 0), 100);
    }

    private function calculate_pronounceability($domain) {
        $score = 50; // Base score
        
        // Check for difficult consonant clusters
        foreach ($this->negative_patterns['difficult_combinations'] as $pattern) {
            if (strpos($domain, $pattern) !== false) {
                $score -= 10;
            }
        }
        
        // Reward good consonant clusters
        foreach ($this->brandable_patterns['consonant_clusters'] as $cluster) {
            if (strpos($domain, $cluster) !== false) {
                $score += 5;
            }
        }
        
        // Check syllable structure (simplified)
        $syllables = $this->estimate_syllables($domain);
        if ($syllables >= 2 && $syllables <= 4) {
            $score += 15;
        } else if ($syllables > 5) {
            $score -= 10;
        }
        
        // Avoid silent letters or unusual combinations
        if (!preg_match('/[qxz]{2,}|[bcdfghjklmnpqrstvwxyz]{4,}/', $domain)) {
            $score += 10;
        }
        
        return min(max($score, 0), 100);
    }

    private function calculate_uniqueness($domain) {
        $score = 50; // Base score
        
        // Check for creative suffixes
        foreach ($this->brandable_patterns['tech_suffixes'] as $suffix) {
            if (substr($domain, -strlen($suffix)) === $suffix) {
                $score += 15;
                break;
            }
        }
        
        foreach ($this->brandable_patterns['creative_suffixes'] as $suffix) {
            if (substr($domain, -strlen($suffix)) === $suffix) {
                $score += 10;
                break;
            }
        }
        
        // Check for modern prefixes
        foreach ($this->brandable_patterns['modern_prefixes'] as $prefix) {
            if (substr($domain, 0, strlen($prefix)) === $prefix) {
                $score += 10;
                break;
            }
        }
        
        // Reward invented/coined words (not in common dictionary)
        if (!$this->is_common_word($domain)) {
            $score += 20;
        }
        
        // Penalize generic terms
        $generic_terms = ['website', 'online', 'internet', 'digital', 'company', 'business'];
        foreach ($generic_terms as $term) {
            if (strpos($domain, $term) !== false) {
                $score -= 15;
                break;
            }
        }
        
        return min(max($score, 0), 100);
    }

    private function calculate_versatility($domain) {
        $score = 50; // Base score
        
        // Check if domain works across different industries
        $neutral_score = 0;
        $specific_indicators = ['tech', 'med', 'law', 'auto', 'food', 'travel'];
        
        foreach ($specific_indicators as $indicator) {
            if (strpos($domain, $indicator) !== false) {
                $score -= 10; // Less versatile
                break;
            } else {
                $neutral_score += 2;
            }
        }
        
        $score += min($neutral_score, 20);
        
        // Reward abstract/metaphorical names
        $abstract_patterns = ['cloud', 'wave', 'spark', 'flow', 'bridge', 'path', 'peak', 'core'];
        foreach ($abstract_patterns as $pattern) {
            if (strpos($domain, $pattern) !== false) {
                $score += 15;
                break;
            }
        }
        
        // Check for international appeal (avoid culture-specific terms)
        if (!preg_match('/[^a-z]/', $domain)) {
            $score += 10;
        }
        
        return min(max($score, 0), 100);
    }

    private function estimate_syllables($word) {
        $word = strtolower($word);
        $syllables = 0;
        $previous_was_vowel = false;
        
        for ($i = 0; $i < strlen($word); $i++) {
            $is_vowel = in_array($word[$i], ['a', 'e', 'i', 'o', 'u', 'y']);
            if ($is_vowel && !$previous_was_vowel) {
                $syllables++;
            }
            $previous_was_vowel = $is_vowel;
        }
        
        // Handle silent 'e'
        if (substr($word, -1) === 'e' && $syllables > 1) {
            $syllables--;
        }
        
        return max($syllables, 1);
    }

    private function is_common_word($word) {
        // Simplified check for common English words
        $common_words = [
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use',
            'home', 'work', 'life', 'time', 'year', 'good', 'great', 'best', 'first', 'last', 'long', 'little', 'own', 'other', 'right', 'big', 'high', 'different', 'small', 'large', 'next', 'early', 'young', 'important', 'few', 'public', 'bad', 'same', 'able'
        ];
        
        return in_array(strtolower($word), $common_words);
    }

    private function assess_brand_potential($domain, $score) {
        if ($score >= 85) return 'Excellent - High brand potential';
        if ($score >= 75) return 'Very Good - Strong brand potential';
        if ($score >= 65) return 'Good - Moderate brand potential';
        if ($score >= 55) return 'Fair - Limited brand potential';
        return 'Poor - Low brand potential';
    }

    private function assess_industry_fit($domain) {
        $industries = [];
        
        // Tech indicators
        if (preg_match('/(tech|app|soft|digital|cyber|data|cloud|ai|io|ly)/', $domain)) {
            $industries[] = 'Technology';
        }
        
        // Creative indicators
        if (preg_match('/(design|creative|art|studio|media|brand)/', $domain)) {
            $industries[] = 'Creative/Design';
        }
        
        // Business indicators
        if (preg_match('/(pro|max|prime|elite|corp|biz)/', $domain)) {
            $industries[] = 'Business/Corporate';
        }
        
        // Health indicators
        if (preg_match('/(health|med|care|wellness|fit)/', $domain)) {
            $industries[] = 'Healthcare';
        }
        
        if (empty($industries)) {
            $industries[] = 'Universal - Suitable for any industry';
        }
        
        return $industries;
    }

    private function get_grade($score) {
        if ($score >= 90) return 'A+ (Excellent)';
        if ($score >= 80) return 'A (Very Good)';
        if ($score >= 70) return 'B (Good)';
        if ($score >= 60) return 'C (Average)';
        if ($score >= 50) return 'D (Below Average)';
        return 'F (Poor)';
    }

    private function get_estimated_value($score) {
        if ($score >= 90) return '$10,000 - $50,000+';
        if ($score >= 80) return '$5,000 - $25,000';
        if ($score >= 70) return '$2,000 - $10,000';
        if ($score >= 60) return '$500 - $5,000';
        if ($score >= 50) return '$100 - $2,000';
        return '$50 - $500';
    }

    private function get_recommendations($domain, $memorability, $pronounceability, $uniqueness, $versatility) {
        $recommendations = [];
        
        if ($memorability < 60) {
            $recommendations[] = "Consider shortening the domain for better memorability";
        }
        
        if ($pronounceability < 60) {
            $recommendations[] = "Simplify pronunciation by avoiding complex letter combinations";
        }
        
        if ($uniqueness < 60) {
            $recommendations[] = "Add creative elements to make the domain more unique";
        }
        
        if ($versatility < 60) {
            $recommendations[] = "Consider making the domain more industry-neutral for broader appeal";
        }
        
        if (strlen($domain) > 15) {
            $recommendations[] = "Shorter domains are generally more brandable";
        }
        
        if (preg_match('/[0-9\-_]/', $domain)) {
            $recommendations[] = "Remove numbers and special characters for better branding";
        }
        
        return $recommendations;
    }
}
