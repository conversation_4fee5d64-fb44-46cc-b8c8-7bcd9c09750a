<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Exact_match_checker extends FrontController {
    
    // High-value exact match keywords
    private $exact_match_keywords = [
        // Financial Services
        'insurance' => 95, 'mortgage' => 90, 'loans' => 85, 'credit' => 80, 'banking' => 85,
        'finance' => 80, 'investment' => 85, 'trading' => 75, 'forex' => 70, 'crypto' => 75,
        
        // Legal Services
        'lawyer' => 90, 'attorney' => 90, 'legal' => 80, 'law' => 75, 'divorce' => 85,
        'injury' => 80, 'accident' => 75, 'criminal' => 70, 'immigration' => 75,
        
        // Medical/Health
        'doctor' => 85, 'medical' => 80, 'health' => 75, 'dental' => 80, 'surgery' => 85,
        'clinic' => 75, 'hospital' => 80, 'pharmacy' => 75, 'therapy' => 70,
        
        // Real Estate
        'realestate' => 90, 'homes' => 85, 'property' => 80, 'realtor' => 85, 'houses' => 80,
        'apartments' => 75, 'condos' => 70, 'commercial' => 75, 'land' => 70,
        
        // Technology
        'software' => 80, 'app' => 75, 'tech' => 70, 'digital' => 65, 'web' => 70,
        'hosting' => 75, 'cloud' => 70, 'security' => 75, 'data' => 65,
        
        // Business Services
        'marketing' => 75, 'advertising' => 70, 'consulting' => 75, 'accounting' => 80,
        'business' => 70, 'services' => 65, 'solutions' => 60, 'company' => 60,
        
        // E-commerce
        'shop' => 70, 'store' => 70, 'buy' => 65, 'sell' => 65, 'market' => 70,
        'ecommerce' => 75, 'retail' => 70, 'wholesale' => 65,
        
        // Travel & Hospitality
        'travel' => 80, 'hotel' => 85, 'vacation' => 75, 'tours' => 70, 'flights' => 80,
        'booking' => 75, 'resort' => 75, 'cruise' => 70,
        
        // Education
        'education' => 70, 'school' => 75, 'university' => 80, 'college' => 75, 'training' => 70,
        'course' => 65, 'learning' => 65, 'online' => 60,
        
        // Automotive
        'car' => 75, 'auto' => 75, 'cars' => 75, 'truck' => 70, 'motorcycle' => 65,
        'dealer' => 70, 'parts' => 65, 'repair' => 70,
        
        // Arabic Keywords
        'عقار' => 85, 'سيارات' => 80, 'تجارة' => 75, 'خدمات' => 70, 'تعليم' => 75,
        'صحة' => 80, 'سفر' => 75, 'فنادق' => 80, 'مطاعم' => 75, 'تسوق' => 70
    ];

    public function __construct() {
        parent::__construct();
        if (!$this->options->get('exact-match-status')) {
            if($this->options->get('search-status'))
                return redirect(base_url());
            else if($this->options->get('generator-status'))
                return redirect(base_url('domain-generator'));
            else if($this->options->get('whois-status'))
                return redirect(base_url('whois'));
            else if($this->options->get('ip-status'))
                return redirect(base_url('ip-lookup'));
            else if($this->options->get('location-status'))
                return redirect(base_url('location'));
            else if($this->options->get('dns-status'))
                return redirect(base_url('dns-lookup'));
            else if($this->options->get('blocklist-status'))
                return redirect(base_url('blocklist-lookup'));
            else if($this->options->get('open-ports-status'))
                return redirect(base_url('open-ports-lookup'));
            else {
                $this->output->set_status_header('404');
                return redirect(base_url('404'));
            }
        }
        $this->load->model('Modules/TldsModel');
    }

    public function index() {
        $this->lang->load('tools', 'main');
        $this->theme->view('pages/exact_match_checker', [
            'title'       => $this->options->get('seo-exact-match-title'),
            'description' => $this->options->get('seo-exact-match-description'),
            'keywords'    => $this->options->get('seo-exact-match-keywords'),
            'canonical'   => base_url('exact-match-checker'),
            'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
            'scripts' => [$this->theme->url('assets/js/components/exact_match_checker.js')]
        ]);
    }

    public function query() {
        $this->lang->load('tools', 'main');
        $response = array(
            "type" => "error",
            "message" => lang('errors_invalid_url_unknown')
        );

        $domain = trim($this->input->post('domain'));
        if (!$domain) {
            echo json_encode($response);
            return;
        }

        // Clean domain name
        $clean_domain = strtolower($domain);
        $clean_domain = preg_replace('/\.(com|net|org|co\.uk|ca|ae|sa)$/', '', $clean_domain);

        $evaluation = $this->evaluate_exact_match($clean_domain);
        
        $response = array(
            "type" => "success",
            "domain" => $domain,
            "clean_domain" => $clean_domain,
            "exact_matches" => $evaluation['exact_matches'],
            "partial_matches" => $evaluation['partial_matches'],
            "keyword_density" => $evaluation['keyword_density'],
            "seo_score" => $evaluation['seo_score'],
            "commercial_value" => $evaluation['commercial_value'],
            "total_score" => $evaluation['total_score'],
            "grade" => $evaluation['grade'],
            "estimated_value" => $evaluation['estimated_value'],
            "recommendations" => $evaluation['recommendations']
        );

        echo json_encode($response);
    }

    private function evaluate_exact_match($domain) {
        $exact_matches = $this->find_exact_matches($domain);
        $partial_matches = $this->find_partial_matches($domain);
        $keyword_density = $this->calculate_keyword_density($domain, $exact_matches, $partial_matches);
        $seo_score = $this->calculate_seo_score($exact_matches, $partial_matches);
        $commercial_value = $this->calculate_commercial_value($exact_matches, $partial_matches);
        
        $total_score = ($seo_score + $commercial_value) / 2;
        $grade = $this->get_grade($total_score);
        $estimated_value = $this->get_estimated_value($total_score, $exact_matches);
        $recommendations = $this->get_recommendations($domain, $exact_matches, $partial_matches);

        return [
            'exact_matches' => $exact_matches,
            'partial_matches' => $partial_matches,
            'keyword_density' => $keyword_density,
            'seo_score' => $seo_score,
            'commercial_value' => $commercial_value,
            'total_score' => $total_score,
            'grade' => $grade,
            'estimated_value' => $estimated_value,
            'recommendations' => $recommendations
        ];
    }

    private function find_exact_matches($domain) {
        $matches = [];
        foreach ($this->exact_match_keywords as $keyword => $value) {
            if ($domain === $keyword) {
                $matches[] = [
                    'keyword' => $keyword,
                    'value' => $value,
                    'type' => 'exact',
                    'category' => $this->get_keyword_category($keyword)
                ];
            }
        }
        return $matches;
    }

    private function find_partial_matches($domain) {
        $matches = [];
        foreach ($this->exact_match_keywords as $keyword => $value) {
            if ($domain !== $keyword && strpos($domain, $keyword) !== false) {
                $matches[] = [
                    'keyword' => $keyword,
                    'value' => $value * 0.7, // Reduce value for partial matches
                    'type' => 'partial',
                    'category' => $this->get_keyword_category($keyword)
                ];
            }
        }
        return $matches;
    }

    private function get_keyword_category($keyword) {
        $categories = [
            'financial' => ['insurance', 'mortgage', 'loans', 'credit', 'banking', 'finance', 'investment', 'trading', 'forex', 'crypto'],
            'legal' => ['lawyer', 'attorney', 'legal', 'law', 'divorce', 'injury', 'accident', 'criminal', 'immigration'],
            'medical' => ['doctor', 'medical', 'health', 'dental', 'surgery', 'clinic', 'hospital', 'pharmacy', 'therapy', 'صحة'],
            'realestate' => ['realestate', 'homes', 'property', 'realtor', 'houses', 'apartments', 'condos', 'commercial', 'land', 'عقار'],
            'technology' => ['software', 'app', 'tech', 'digital', 'web', 'hosting', 'cloud', 'security', 'data'],
            'business' => ['marketing', 'advertising', 'consulting', 'accounting', 'business', 'services', 'solutions', 'company', 'خدمات', 'تجارة'],
            'ecommerce' => ['shop', 'store', 'buy', 'sell', 'market', 'ecommerce', 'retail', 'wholesale', 'تسوق'],
            'travel' => ['travel', 'hotel', 'vacation', 'tours', 'flights', 'booking', 'resort', 'cruise', 'سفر', 'فنادق'],
            'education' => ['education', 'school', 'university', 'college', 'training', 'course', 'learning', 'online', 'تعليم'],
            'automotive' => ['car', 'auto', 'cars', 'truck', 'motorcycle', 'dealer', 'parts', 'repair', 'سيارات'],
            'food' => ['مطاعم']
        ];

        foreach ($categories as $category => $keywords) {
            if (in_array($keyword, $keywords)) {
                return $category;
            }
        }
        return 'general';
    }

    private function calculate_keyword_density($domain, $exact_matches, $partial_matches) {
        $total_keywords = count($exact_matches) + count($partial_matches);
        $domain_length = strlen($domain);
        
        if ($domain_length === 0) return 0;
        
        return round(($total_keywords / $domain_length) * 100, 2);
    }

    private function calculate_seo_score($exact_matches, $partial_matches) {
        $score = 0;
        
        foreach ($exact_matches as $match) {
            $score += $match['value'];
        }
        
        foreach ($partial_matches as $match) {
            $score += $match['value'];
        }
        
        return min($score, 100); // Cap at 100
    }

    private function calculate_commercial_value($exact_matches, $partial_matches) {
        $high_value_categories = ['financial', 'legal', 'medical', 'realestate'];
        $score = 0;
        
        foreach ($exact_matches as $match) {
            if (in_array($match['category'], $high_value_categories)) {
                $score += $match['value'] * 1.2; // Boost for high-value categories
            } else {
                $score += $match['value'];
            }
        }
        
        foreach ($partial_matches as $match) {
            if (in_array($match['category'], $high_value_categories)) {
                $score += $match['value'] * 1.2;
            } else {
                $score += $match['value'];
            }
        }
        
        return min($score, 100); // Cap at 100
    }

    private function get_grade($score) {
        if ($score >= 90) return 'A+ (Excellent)';
        if ($score >= 80) return 'A (Very Good)';
        if ($score >= 70) return 'B (Good)';
        if ($score >= 60) return 'C (Average)';
        if ($score >= 50) return 'D (Below Average)';
        return 'F (Poor)';
    }

    private function get_estimated_value($score, $exact_matches) {
        $base_value = 0;
        
        if ($score >= 90) $base_value = 50000;
        else if ($score >= 80) $base_value = 20000;
        else if ($score >= 70) $base_value = 10000;
        else if ($score >= 60) $base_value = 5000;
        else if ($score >= 50) $base_value = 2000;
        else $base_value = 500;
        
        // Boost for exact matches
        if (!empty($exact_matches)) {
            $base_value *= 2;
        }
        
        $min_value = $base_value * 0.5;
        $max_value = $base_value * 2;
        
        return '$' . number_format($min_value) . ' - $' . number_format($max_value);
    }

    private function get_recommendations($domain, $exact_matches, $partial_matches) {
        $recommendations = [];
        
        if (!empty($exact_matches)) {
            $recommendations[] = "Excellent! This domain contains exact match keywords with high commercial value";
        }
        
        if (!empty($partial_matches)) {
            $recommendations[] = "Good partial keyword matches found - consider exact match variations";
        }
        
        if (empty($exact_matches) && empty($partial_matches)) {
            $recommendations[] = "No high-value keywords detected - consider adding commercial terms";
        }
        
        if (strlen($domain) > 15) {
            $recommendations[] = "Consider shorter domain variations for better memorability";
        }
        
        if (count($exact_matches) > 1) {
            $recommendations[] = "Multiple exact matches detected - excellent for SEO and PPC campaigns";
        }
        
        return $recommendations;
    }
}
