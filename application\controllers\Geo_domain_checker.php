<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Geo_domain_checker extends FrontController {
    
    // Geographic importance scoring data
    private $geographic_scores = [
        // Major International Cities - 20 points
        'london' => 20, 'newyork' => 20, 'nyc' => 20, 'tokyo' => 20, 'paris' => 20, 'singapore' => 20,
        'hongkong' => 20, 'dubai' => 20, 'losangeles' => 20, 'chicago' => 20, 'sydney' => 20,
        
        // Major National Cities - 15 points
        'dallas' => 15, 'manchester' => 15, 'toronto' => 15, 'miami' => 15, 'boston' => 15,
        'seattle' => 15, 'denver' => 15, 'atlanta' => 15, 'phoenix' => 15, 'philadelphia' => 15,
        'houston' => 15, 'detroit' => 15, 'vancouver' => 15, 'montreal' => 15, 'calgary' => 15,
        'melbourne' => 15, 'brisbane' => 15, 'perth' => 15, 'birmingham' => 15, 'leeds' => 15,
        'glasgow' => 15, 'liverpool' => 15, 'edinburgh' => 15, 'bristol' => 15, 'cardiff' => 15,
        
        // Regional Hubs - 10 points
        'austin' => 10, 'brighton' => 10, 'winnipeg' => 10, 'nashville' => 10, 'portland' => 10,
        'milwaukee' => 10, 'sacramento' => 10, 'tampa' => 10, 'orlando' => 10, 'charlotte' => 10,
        'pittsburgh' => 10, 'cincinnati' => 10, 'indianapolis' => 10, 'columbus' => 10,
        'jacksonville' => 10, 'memphis' => 10, 'richmond' => 10, 'norfolk' => 10, 'raleigh' => 10,
        
        // Arabic Cities
        'riyadh' => 15, 'jeddah' => 15, 'mecca' => 15, 'medina' => 15, 'dammam' => 10,
        'cairo' => 15, 'alexandria' => 10, 'giza' => 10,
        'beirut' => 10, 'amman' => 10, 'baghdad' => 10, 'kuwait' => 15, 'doha' => 15,
        'manama' => 10, 'muscat' => 10, 'abu_dhabi' => 15, 'sharjah' => 10,
        'casablanca' => 10, 'rabat' => 10, 'tunis' => 10, 'algiers' => 10,
        
        // Default for unrecognized cities
        'default' => 5
    ];

    public function __construct() {
        parent::__construct();
        if (!$this->options->get('geo-domain-status')) {
            if($this->options->get('search-status'))
                return redirect(base_url());
            else if($this->options->get('generator-status'))
                return redirect(base_url('domain-generator'));
            else if($this->options->get('whois-status'))
                return redirect(base_url('whois'));
            else if($this->options->get('ip-status'))
                return redirect(base_url('ip-lookup'));
            else if($this->options->get('location-status'))
                return redirect(base_url('location'));
            else if($this->options->get('dns-status'))
                return redirect(base_url('dns-lookup'));
            else if($this->options->get('blocklist-status'))
                return redirect(base_url('blocklist-lookup'));
            else if($this->options->get('open-ports-status'))
                return redirect(base_url('open-ports-lookup'));
            else {
                $this->output->set_status_header('404');
                return redirect(base_url('404'));
            }
        }
        $this->load->model('Modules/TldsModel');
    }

    public function index() {
        $this->lang->load('tools', 'main');
        $this->theme->view('pages/geo_domain_checker', [
            'title'       => $this->options->get('seo-geo-domain-title'),
            'description' => $this->options->get('seo-geo-domain-description'),
            'keywords'    => $this->options->get('seo-geo-domain-keywords'),
            'canonical'   => base_url('geo-domain-checker'),
            'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
            'scripts' => [$this->theme->url('assets/js/components/geo_domain_checker.js')]
        ]);
    }

    public function query() {
        $this->lang->load('tools', 'main');
        $response = array(
            "type" => "error",
            "message" => lang('errors_invalid_url_unknown')
        );

        $domain = trim($this->input->post('domain'));
        if (!$domain) {
            echo json_encode($response);
            return;
        }

        // Clean domain name
        $clean_domain = strtolower($domain);
        $clean_domain = preg_replace('/\.(com|net|org|co\.uk|ca|ae|sa)$/', '', $clean_domain);

        $evaluation = $this->evaluate_geo_domain($clean_domain);
        
        $response = array(
            "type" => "success",
            "domain" => $domain,
            "clean_domain" => $clean_domain,
            "geographic_score" => $evaluation['geographic_score'],
            "detected_cities" => $evaluation['detected_cities'],
            "commercial_potential" => $evaluation['commercial_potential'],
            "total_score" => $evaluation['total_score'],
            "grade" => $evaluation['grade'],
            "estimated_value" => $evaluation['estimated_value'],
            "recommendations" => $evaluation['recommendations']
        );

        echo json_encode($response);
    }

    private function evaluate_geo_domain($domain) {
        $geographic_score = $this->evaluate_geographic($domain);
        $detected_cities = $this->detect_cities($domain);
        $commercial_potential = $this->evaluate_commercial_potential($domain);
        
        $total_score = $geographic_score + $commercial_potential;
        $grade = $this->get_grade($total_score);
        $estimated_value = $this->get_estimated_value($total_score);
        $recommendations = $this->get_recommendations($domain, $geographic_score, $commercial_potential);

        return [
            'geographic_score' => $geographic_score,
            'detected_cities' => $detected_cities,
            'commercial_potential' => $commercial_potential,
            'total_score' => $total_score,
            'grade' => $grade,
            'estimated_value' => $estimated_value,
            'recommendations' => $recommendations
        ];
    }

    private function evaluate_geographic($domain) {
        // Check for exact city matches
        foreach ($this->geographic_scores as $city => $score) {
            if ($city !== 'default' && strpos($domain, $city) !== false) {
                return $score;
            }
        }

        // Check for common city patterns
        if (preg_match('/^[a-z]+city/', $domain) || preg_match('/city[a-z]*$/', $domain)) {
            return 5; // Assume local city
        }

        return 3; // Neighborhood/suburb level
    }

    private function detect_cities($domain) {
        $detected = [];
        foreach ($this->geographic_scores as $city => $score) {
            if ($city !== 'default' && strpos($domain, $city) !== false) {
                $detected[] = [
                    'city' => $city,
                    'score' => $score,
                    'importance' => $this->get_city_importance($score)
                ];
            }
        }
        return $detected;
    }

    private function get_city_importance($score) {
        if ($score >= 20) return 'Major International City';
        if ($score >= 15) return 'Major National City';
        if ($score >= 10) return 'Regional Hub';
        return 'Local City';
    }

    private function evaluate_commercial_potential($domain) {
        $commercial_keywords = [
            'high' => ['realestate', 'properties', 'homes', 'lawyers', 'attorney', 'legal',
                      'doctors', 'medical', 'health', 'hotels', 'restaurants', 'dining',
                      'auto', 'cars', 'dealership', 'insurance', 'mortgage', 'loan',
                      'plumbing', 'electrician', 'roofing', 'hvac', 'construction'],
            'medium' => ['services', 'business', 'company', 'events', 'wedding', 'catering',
                        'travel', 'tours', 'vacation', 'shopping', 'store', 'market',
                        'consulting', 'marketing', 'design', 'photography', 'fitness'],
            'low' => ['news', 'information', 'guide', 'community', 'forum', 'social',
                     'weather', 'traffic', 'local', 'blog', 'directory']
        ];

        // Check high commercial value keywords
        foreach ($commercial_keywords['high'] as $keyword) {
            if (strpos($domain, $keyword) !== false) {
                return 25;
            }
        }

        // Check medium commercial value keywords
        foreach ($commercial_keywords['medium'] as $keyword) {
            if (strpos($domain, $keyword) !== false) {
                return 15;
            }
        }

        // Check low commercial value keywords
        foreach ($commercial_keywords['low'] as $keyword) {
            if (strpos($domain, $keyword) !== false) {
                return 5;
            }
        }

        return 5; // Default score
    }

    private function get_grade($score) {
        if ($score >= 40) return 'A+ (Excellent)';
        if ($score >= 35) return 'A (Very Good)';
        if ($score >= 30) return 'B (Good)';
        if ($score >= 25) return 'C (Average)';
        if ($score >= 20) return 'D (Below Average)';
        return 'F (Poor)';
    }

    private function get_estimated_value($score) {
        if ($score >= 40) return '$5,000 - $20,000+';
        if ($score >= 35) return '$2,000 - $8,000';
        if ($score >= 30) return '$500 - $3,000';
        if ($score >= 25) return '$200 - $1,000';
        if ($score >= 20) return '$50 - $500';
        return '$10 - $200';
    }

    private function get_recommendations($domain, $geo_score, $commercial_score) {
        $recommendations = [];
        
        if ($geo_score >= 15) {
            $recommendations[] = "High geographic value - Great for local businesses";
        }
        
        if ($commercial_score >= 20) {
            $recommendations[] = "Strong commercial potential - Suitable for business use";
        }
        
        if ($geo_score < 10) {
            $recommendations[] = "Consider adding city name for better local SEO";
        }
        
        if ($commercial_score < 10) {
            $recommendations[] = "Add commercial keywords to increase business value";
        }
        
        if (strlen($domain) > 20) {
            $recommendations[] = "Consider shorter domain for better memorability";
        }
        
        return $recommendations;
    }
}
