<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Keyword_checker extends FrontController {
    
    // Keyword categories and their search volumes (estimated)
    private $keyword_categories = [
        'high_volume' => [
            'insurance' => 1000000, 'loans' => 900000, 'mortgage' => 800000, 'credit' => 750000,
            'lawyer' => 700000, 'attorney' => 650000, 'doctor' => 600000, 'health' => 550000,
            'travel' => 500000, 'hotel' => 450000, 'car' => 400000, 'real estate' => 350000,
            'education' => 300000, 'software' => 280000, 'marketing' => 250000, 'business' => 200000
        ],
        'medium_volume' => [
            'dental' => 150000, 'legal' => 140000, 'medical' => 130000, 'property' => 120000,
            'vacation' => 110000, 'restaurant' => 100000, 'fitness' => 90000, 'beauty' => 80000,
            'consulting' => 70000, 'accounting' => 60000, 'design' => 55000, 'photography' => 50000
        ],
        'low_volume' => [
            'plumbing' => 40000, 'electrician' => 35000, 'roofing' => 30000, 'hvac' => 25000,
            'landscaping' => 20000, 'cleaning' => 18000, 'catering' => 15000, 'tutoring' => 12000,
            'pet grooming' => 10000, 'locksmith' => 8000, 'handyman' => 7000, 'massage' => 6000
        ],
        'arabic_keywords' => [
            'تأمين' => 200000, 'قروض' => 180000, 'عقار' => 160000, 'سيارات' => 150000,
            'محامي' => 140000, 'طبيب' => 130000, 'صحة' => 120000, 'سفر' => 110000,
            'فنادق' => 100000, 'تعليم' => 90000, 'تجارة' => 80000, 'خدمات' => 70000,
            'مطاعم' => 60000, 'تسوق' => 50000, 'جمال' => 40000, 'رياضة' => 35000
        ]
    ];

    private $keyword_difficulty = [
        'very_high' => ['insurance', 'loans', 'mortgage', 'credit', 'lawyer', 'attorney', 'تأمين', 'قروض'],
        'high' => ['doctor', 'health', 'travel', 'hotel', 'car', 'real estate', 'طبيب', 'صحة', 'سفر', 'عقار'],
        'medium' => ['dental', 'legal', 'medical', 'property', 'vacation', 'restaurant', 'محامي', 'فنادق'],
        'low' => ['fitness', 'beauty', 'consulting', 'accounting', 'design', 'photography', 'جمال', 'رياضة'],
        'very_low' => ['plumbing', 'electrician', 'roofing', 'hvac', 'landscaping', 'cleaning']
    ];

    public function __construct() {
        parent::__construct();
        if (!$this->options->get('keyword-status')) {
            if($this->options->get('search-status'))
                return redirect(base_url());
            else if($this->options->get('generator-status'))
                return redirect(base_url('domain-generator'));
            else if($this->options->get('whois-status'))
                return redirect(base_url('whois'));
            else if($this->options->get('ip-status'))
                return redirect(base_url('ip-lookup'));
            else if($this->options->get('location-status'))
                return redirect(base_url('location'));
            else if($this->options->get('dns-status'))
                return redirect(base_url('dns-lookup'));
            else if($this->options->get('blocklist-status'))
                return redirect(base_url('blocklist-lookup'));
            else if($this->options->get('open-ports-status'))
                return redirect(base_url('open-ports-lookup'));
            else {
                $this->output->set_status_header('404');
                return redirect(base_url('404'));
            }
        }
        $this->load->model('Modules/TldsModel');
    }

    public function index() {
        $this->lang->load('tools', 'main');
        $this->theme->view('pages/keyword_checker', [
            'title'       => $this->options->get('seo-keyword-title'),
            'description' => $this->options->get('seo-keyword-description'),
            'keywords'    => $this->options->get('seo-keyword-keywords'),
            'canonical'   => base_url('keyword-checker'),
            'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
            'scripts' => [$this->theme->url('assets/js/components/keyword_checker.js')]
        ]);
    }

    public function query() {
        $this->lang->load('tools', 'main');
        $response = array(
            "type" => "error",
            "message" => lang('errors_invalid_url_unknown')
        );

        $domain = trim($this->input->post('domain'));
        if (!$domain) {
            echo json_encode($response);
            return;
        }

        // Clean domain name
        $clean_domain = strtolower($domain);
        $clean_domain = preg_replace('/\.(com|net|org|co\.uk|ca|ae|sa)$/', '', $clean_domain);

        $evaluation = $this->evaluate_keywords($clean_domain);
        
        $response = array(
            "type" => "success",
            "domain" => $domain,
            "clean_domain" => $clean_domain,
            "detected_keywords" => $evaluation['detected_keywords'],
            "search_volume_score" => $evaluation['search_volume_score'],
            "competition_score" => $evaluation['competition_score'],
            "keyword_density" => $evaluation['keyword_density'],
            "seo_potential" => $evaluation['seo_potential'],
            "ppc_potential" => $evaluation['ppc_potential'],
            "total_score" => $evaluation['total_score'],
            "grade" => $evaluation['grade'],
            "estimated_value" => $evaluation['estimated_value'],
            "recommendations" => $evaluation['recommendations']
        );

        echo json_encode($response);
    }

    private function evaluate_keywords($domain) {
        $detected_keywords = $this->detect_keywords($domain);
        $search_volume_score = $this->calculate_search_volume_score($detected_keywords);
        $competition_score = $this->calculate_competition_score($detected_keywords);
        $keyword_density = $this->calculate_keyword_density($domain, $detected_keywords);
        $seo_potential = $this->calculate_seo_potential($detected_keywords);
        $ppc_potential = $this->calculate_ppc_potential($detected_keywords);
        
        $total_score = ($search_volume_score + $competition_score + $seo_potential + $ppc_potential) / 4;
        $grade = $this->get_grade($total_score);
        $estimated_value = $this->get_estimated_value($total_score, $detected_keywords);
        $recommendations = $this->get_recommendations($domain, $detected_keywords, $search_volume_score, $competition_score);

        return [
            'detected_keywords' => $detected_keywords,
            'search_volume_score' => $search_volume_score,
            'competition_score' => $competition_score,
            'keyword_density' => $keyword_density,
            'seo_potential' => $seo_potential,
            'ppc_potential' => $ppc_potential,
            'total_score' => $total_score,
            'grade' => $grade,
            'estimated_value' => $estimated_value,
            'recommendations' => $recommendations
        ];
    }

    private function detect_keywords($domain) {
        $keywords = [];
        $all_keywords = array_merge(
            $this->keyword_categories['high_volume'],
            $this->keyword_categories['medium_volume'],
            $this->keyword_categories['low_volume'],
            $this->keyword_categories['arabic_keywords']
        );

        foreach ($all_keywords as $keyword => $volume) {
            if (strpos($domain, str_replace(' ', '', $keyword)) !== false) {
                $keywords[] = [
                    'keyword' => $keyword,
                    'volume' => $volume,
                    'category' => $this->get_keyword_volume_category($keyword),
                    'difficulty' => $this->get_keyword_difficulty($keyword),
                    'language' => $this->detect_language($keyword)
                ];
            }
        }

        return $keywords;
    }

    private function get_keyword_volume_category($keyword) {
        foreach ($this->keyword_categories as $category => $keywords) {
            if (array_key_exists($keyword, $keywords)) {
                return $category;
            }
        }
        return 'unknown';
    }

    private function get_keyword_difficulty($keyword) {
        foreach ($this->keyword_difficulty as $difficulty => $keywords) {
            if (in_array($keyword, $keywords)) {
                return $difficulty;
            }
        }
        return 'medium';
    }

    private function detect_language($keyword) {
        // Simple language detection
        if (preg_match('/[\x{0600}-\x{06FF}]/u', $keyword)) {
            return 'arabic';
        }
        return 'english';
    }

    private function calculate_search_volume_score($keywords) {
        if (empty($keywords)) return 0;
        
        $total_volume = 0;
        foreach ($keywords as $keyword) {
            $total_volume += $keyword['volume'];
        }
        
        // Normalize to 0-100 scale
        if ($total_volume >= 1000000) return 100;
        if ($total_volume >= 500000) return 90;
        if ($total_volume >= 200000) return 80;
        if ($total_volume >= 100000) return 70;
        if ($total_volume >= 50000) return 60;
        if ($total_volume >= 20000) return 50;
        if ($total_volume >= 10000) return 40;
        if ($total_volume >= 5000) return 30;
        if ($total_volume >= 1000) return 20;
        return 10;
    }

    private function calculate_competition_score($keywords) {
        if (empty($keywords)) return 0;
        
        $competition_scores = [
            'very_low' => 90,
            'low' => 75,
            'medium' => 60,
            'high' => 40,
            'very_high' => 20
        ];
        
        $total_score = 0;
        foreach ($keywords as $keyword) {
            $total_score += $competition_scores[$keyword['difficulty']];
        }
        
        return round($total_score / count($keywords));
    }

    private function calculate_keyword_density($domain, $keywords) {
        if (empty($keywords)) return 0;
        
        $keyword_chars = 0;
        foreach ($keywords as $keyword) {
            $keyword_chars += strlen(str_replace(' ', '', $keyword['keyword']));
        }
        
        $domain_length = strlen($domain);
        return $domain_length > 0 ? round(($keyword_chars / $domain_length) * 100, 2) : 0;
    }

    private function calculate_seo_potential($keywords) {
        if (empty($keywords)) return 0;
        
        $seo_score = 0;
        foreach ($keywords as $keyword) {
            // Higher volume = better SEO potential
            $volume_score = min($keyword['volume'] / 10000, 50);
            
            // Lower competition = better SEO potential
            $competition_multiplier = [
                'very_low' => 1.5,
                'low' => 1.2,
                'medium' => 1.0,
                'high' => 0.8,
                'very_high' => 0.5
            ];
            
            $seo_score += $volume_score * $competition_multiplier[$keyword['difficulty']];
        }
        
        return min($seo_score, 100);
    }

    private function calculate_ppc_potential($keywords) {
        if (empty($keywords)) return 0;
        
        // High competition keywords usually have high PPC value
        $ppc_scores = [
            'very_high' => 95,
            'high' => 85,
            'medium' => 70,
            'low' => 50,
            'very_low' => 30
        ];
        
        $total_score = 0;
        foreach ($keywords as $keyword) {
            $total_score += $ppc_scores[$keyword['difficulty']];
        }
        
        return round($total_score / count($keywords));
    }

    private function get_grade($score) {
        if ($score >= 90) return 'A+ (Excellent)';
        if ($score >= 80) return 'A (Very Good)';
        if ($score >= 70) return 'B (Good)';
        if ($score >= 60) return 'C (Average)';
        if ($score >= 50) return 'D (Below Average)';
        return 'F (Poor)';
    }

    private function get_estimated_value($score, $keywords) {
        $base_value = 0;
        
        if ($score >= 90) $base_value = 25000;
        else if ($score >= 80) $base_value = 15000;
        else if ($score >= 70) $base_value = 8000;
        else if ($score >= 60) $base_value = 4000;
        else if ($score >= 50) $base_value = 2000;
        else $base_value = 500;
        
        // Boost for high-competition keywords
        foreach ($keywords as $keyword) {
            if (in_array($keyword['difficulty'], ['very_high', 'high'])) {
                $base_value *= 1.5;
                break;
            }
        }
        
        $min_value = $base_value * 0.6;
        $max_value = $base_value * 2;
        
        return '$' . number_format($min_value) . ' - $' . number_format($max_value);
    }

    private function get_recommendations($domain, $keywords, $volume_score, $competition_score) {
        $recommendations = [];
        
        if (empty($keywords)) {
            $recommendations[] = "No high-value keywords detected - consider adding relevant industry terms";
        } else {
            $recommendations[] = "Keywords detected: " . count($keywords) . " relevant terms found";
        }
        
        if ($volume_score >= 80) {
            $recommendations[] = "Excellent search volume potential - great for SEO";
        } else if ($volume_score < 40) {
            $recommendations[] = "Low search volume - consider adding more popular keywords";
        }
        
        if ($competition_score >= 80) {
            $recommendations[] = "Low competition keywords - easier to rank";
        } else if ($competition_score < 40) {
            $recommendations[] = "High competition keywords - requires strong SEO strategy";
        }
        
        // Check for Arabic keywords
        $has_arabic = false;
        foreach ($keywords as $keyword) {
            if ($keyword['language'] === 'arabic') {
                $has_arabic = true;
                break;
            }
        }
        
        if ($has_arabic) {
            $recommendations[] = "Arabic keywords detected - excellent for Middle East market";
        }
        
        if (strlen($domain) > 20) {
            $recommendations[] = "Consider shorter domain for better keyword focus";
        }
        
        return $recommendations;
    }
}
