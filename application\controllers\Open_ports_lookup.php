<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Open_ports_lookup extends FrontController {

	public function __construct() {
        parent::__construct();
		if (!$this->options->get('open-ports-status')) {
			if($this->options->get('search-status'))
			return redirect(base_url());
			else if($this->options->get('generator-status'))
			return redirect(base_url('domain-generator'));
			else if($this->options->get('whois-status'))
			return redirect(base_url('whois'));
			else if($this->options->get('ip-status'))
			return redirect(base_url('ip-lookup'));
			else if($this->options->get('location-status'))
			return redirect(base_url('location'));
			else if($this->options->get('dns-status'))
			return redirect(base_url('dns-lookup'));
			else if($this->options->get('blocklist-status'))
			return redirect(base_url('blocklist-lookup'));
			else {
				$this->output->set_status_header('404');
				return redirect(base_url('404'));
			}
		}
        $this->load->model('Modules/TldsModel');
    }

	public function index() {
        $this->lang->load('tools', 'main');
		$this->theme->view('pages/ports', [
		 	'title'       => $this->options->get('seo-search-title'),
		 	'description' => $this->options->get('seo-search-description'),
			'keywords' => $this->options->get('seo-search-keywords'),
			'canonical' => base_url(),
			'js_errors' => [
                'invalid_domain' => lang('errors_invalid_domain'),
                'invalid_url_unknown' => lang('errors_invalid_url_unknown'),
            ],
			'scripts' => [$this->theme->url('assets/js/jquery.min.js'), $this->theme->url('assets/js/components/ports.js')]
		]);
	}

	public function set_mode() {
		if(get_mode() == 'light')
		set_mode('dark');
		else
		set_mode('light');
		!empty($this->input->get('redirect_url')) ? redirect($this->input->get('redirect_url')) : redirect(base_url());
		exit();
	}

	public function query() {
		$this->lang->load('tools', 'main');
		$url = trim($this->input->post('url'));
		if(!is_valid_ip_address($url))
		$url = gethostbyname($url);
		if(is_valid_ip_address($url)) {
			$response['domain'] = $url;
			$response['status'] = 'blank';
            $response['ports'] =   array(
				array('port' => 7, 'service' => 'Echo', 'protocol' => 'TCP, UDP'),
				array('port' => 20, 'service' => 'FTP - Data', 'protocol' => 'TCP'),
				array('port' => 21, 'service' => 'FTP', 'protocol' => 'TCP, UDP, SCTP'),
				array('port' => 22, 'service' => 'SSH-SCP', 'protocol' => 'TCP, UDP, SCTP'),
				array('port' => 23, 'service' => 'Telnet', 'protocol' => 'TCP'),
				array('port' => 25, 'service' => 'SMTP', 'protocol' => 'TCP'),
				array('port' => 53, 'service' => 'DNS', 'protocol' => 'TCP, UDP'),
				array('port' => 69, 'service' => 'TFTP', 'protocol' => 'UDP'),
				array('port' => 80, 'service' => 'HTTP', 'protocol' => 'TCP, UDP, SCTP'),
				array('port' => 67, 'service' => 'DHCP', 'protocol' => 'UDP'),
				array('port' => 68, 'service' => 'DHCP', 'protocol' => 'UDP'),
				array('port' => 42, 'service' => 'Name Server', 'protocol' => 'TCP, UDP'),
				array('port' => 88, 'service' => 'Kerberos', 'protocol' => 'TCP, UDP'),
				array('port' => 102, 'service' => 'Iso-tsap', 'protocol' => 'TCP'),
				array('port' => 110, 'service' => 'POP3', 'protocol' => 'TCP'),
				array('port' => 135, 'service' => 'Microsoft EPMAP', 'protocol' => 'TCP, UDP'),
				array('port' => 137, 'service' => 'NetBIOS-ns', 'protocol' => 'TCP, UDP'),
				array('port' => 139, 'service' => 'NetBIOS-ssn', 'protocol' => 'TCP, UDP'),
				array('port' => 143, 'service' => 'IMAP4', 'protocol' => 'TCP, UDP'),
				array('port' => 381, 'service' => 'HP Openview', 'protocol' => 'TCP, UDP'),
				array('port' => 383, 'service' => 'HP Openview', 'protocol' => 'TCP, UDP'),
				array('port' => 443, 'service' => 'HTTP over SSL', 'protocol' => 'TCP, UDP, SCTP'),
				array('port' => 464, 'service' => 'Kerberos', 'protocol' => 'TCP, UDP'),
				array('port' => 465, 'service' => 'SMTP over TLS/SSL, SSM', 'protocol' => 'TCP'),
				array('port' => 587, 'service' => 'SMTP', 'protocol' => 'TCP'),
				array('port' => 593, 'service' => 'Microsoft DCOM', 'protocol' => 'TCP, UDP'),
				array('port' => 636, 'service' => 'LDAP over TLS/SSL', 'protocol' => 'TCP, UDP'),
				array('port' => 691, 'service' => 'MS Exchange', 'protocol' => 'TCP'),
				array('port' => 902, 'service' => 'VMware Server', 'protocol' => 'TCP, UDP'),
				array('port' => 989, 'service' => 'FTP over SSL', 'protocol' => 'TCP, UDP'),
				array('port' => 990, 'service' => 'FTP over SSL', 'protocol' => 'TCP, UDP'),
				array('port' => 993, 'service' => 'IMAP4 over SSL', 'protocol' => 'TCP'),
				array('port' => 995, 'service' => 'POP3 over SSL', 'protocol' => 'TCP, UDP'),
				array('port' => 1025, 'service' => 'Microsoft RPC', 'protocol' => 'TCP'),
				array('port' => 1194, 'service' => 'OpenVPN', 'protocol' => 'TCP, UDP'),
				array('port' => 1337, 'service' => 'WASTE', 'protocol' => 'TCP'),
				array('port' => 1589, 'service' => 'Cisco VQP', 'protocol' => 'TCP, UDP'),
				array('port' => 1725, 'service' => 'Steam', 'protocol' => 'UDP'),
				array('port' => 2082, 'service' => 'cPanel', 'protocol' => 'TCP'),
				array('port' => 2083, 'service' => 'radsec, cPanel', 'protocol' => 'TCP, UDP'),
				array('port' => 2483, 'service' => 'Oracle DB', 'protocol' => 'TCP, UDP'),
				array('port' => 2484, 'service' => 'Oracle DB', 'protocol' => 'TCP, UDP'),
				array('port' => 2967, 'service' => 'Symantec AV', 'protocol' => 'TCP, UDP'),
				array('port' => 3074, 'service' => 'XBOX Live', 'protocol' => 'TCP, UDP'),
				array('port' => 3306, 'service' => 'MySQL', 'protocol' => 'TCP'),
				array('port' => 3724, 'service' => 'World of Warcraft', 'protocol' => 'TCP, UDP'),
				array('port' => 4664, 'service' => 'Google Desktop', 'protocol' => 'TCP'),
				array('port' => 5432, 'service' => 'PostgreSQL', 'protocol' => 'TCP'),
				array('port' => 5900, 'service' => 'RFB/VNC Server', 'protocol' => 'TCP, UDP'),
				array('port' => 6667, 'service' => 'IRC', 'protocol' => 'TCP'),
				array('port' => 6697, 'service' => 'IRC SSL/TLS', 'protocol' => 'TCP'),
				array('port' => 6881, 'service' => 'BitTorrent', 'protocol' => 'TCP'),
				array('port' => 6999, 'service' => 'BitTorrent', 'protocol' => 'TCP, UDP'),
				array('port' => 6970, 'service' => 'Quicktime', 'protocol' => 'TCP, UDP'),
				array('port' => 8086, 'service' => 'Kaspersky AV', 'protocol' => 'TCP'),
				array('port' => 8087, 'service' => 'Kaspersky AV', 'protocol' => 'UDP'),
				array('port' => 8222, 'service' => 'VMware Server', 'protocol' => 'TCP, UDP'),
				array('port' => 9100, 'service' => 'PDL', 'protocol' => 'TCP'),
				array('port' => 10000, 'service' => 'BackupExec', 'protocol' => 'TCP'),
				array('port' => 12345, 'service' => 'NetBus', 'protocol' => 'TCP'),
				array('port' => 27374, 'service' => 'Sub7', 'protocol' => 'TCP'),
				array('port' => 31337, 'service' => 'Back Orifice', 'protocol' => 'TCP, UDP'),
			);
		} else {
			$response = array(
				"type" => "error",
				"message" => lang('errors_invalid_url_ip')
			);
		}
        echo json_encode($response);
	}

	public function single_query() {
		$response = array(
            "status" => "blank",
            "link" => "#",
        );
		$port = 0;
		$domain = "";
		if($this->input->post('port')) {
			$port = $this->input->post('port');
			$domain = $this->input->post('domain');
		}
		if($this->ping_port($domain, $port))
		$response['status'] = "blocked";
		else
		$response['status'] = "clear";
		$response['link']   = $domain;
		$response['domain']   = $domain;
		echo json_encode($response);
	}
    public function ping_port($host, $port) {
		$connection = @fsockopen($host, $port, $errno, $errstr, 2);
		if (is_resource($connection)) {
			fclose($connection);
			return true;
		}
		return false;
    }

    public function ping_port2($host, $port) {
		$is_open_tcp = false;
		$is_open_udp = false;
        $connection_tcp = @stream_socket_client("tcp://$host:$port", $errno, $errstr, 3);
        if ($connection_tcp) {
			$is_open_tcp = true;
			fclose($connection_tcp);
		} else {
			$is_open_tcp = false;
		}
		$connection_udp = @stream_socket_client("tcp://$host:$port", $errno, $errstr, 3);
        if ($connection_udp) {
			$is_open_udp = true;
			fclose($connection_udp);
		} else {
			$is_open_udp = false;
		}

		return ($is_open_tcp || $is_open_udp);
    }
	public function ping_port1($host, $port) {
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, "tcp://$host:$port");
		curl_setopt($ch, CURLOPT_NOBODY, true);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
		curl_exec($ch);
		$is_open_tcp = (curl_errno($ch) === 0) ? true : false;
		curl_close($ch);
		$ch1 = curl_init();
		curl_setopt($ch1, CURLOPT_URL, "udp://$host:$port");
		curl_setopt($ch1, CURLOPT_NOBODY, true);
		curl_setopt($ch1, CURLOPT_CONNECTTIMEOUT, 3);
		curl_exec($ch1);
		$is_open_udp = (curl_errno($ch1) === 0) ? true : false;
		curl_close($ch1);
		return ($is_open_tcp || $is_open_udp);
	}
}