<?php defined('BASEPATH') || exit('Access Denied.');

class Migrate extends AdminController {
    
    public function __construct() {
        parent::__construct();
        $this->load->library('migration');
    }
    
    public function index() {
        // Run migrations to latest version
        if ($this->migration->latest() === FALSE) {
            show_error($this->migration->error_string());
        } else {
            echo "Migrations completed successfully!";
            echo "<br><a href='" . admin_base_url('options') . "'>Go to Options</a>";
        }
    }
    
    public function to_version($version) {
        // Run migration to specific version
        if ($this->migration->version($version) === FALSE) {
            show_error($this->migration->error_string());
        } else {
            echo "Migration to version $version completed successfully!";
            echo "<br><a href='" . admin_base_url('options') . "'>Go to Options</a>";
        }
    }
}
