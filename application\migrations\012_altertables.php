<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Altertables extends CI_Migration {
	
    public function alter_table_type() {
        $this->load->database();
        $this->db->query("ALTER TABLE pages CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
		$this->db->query("ALTER TABLE blog CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    }

    public function up() {
        $this->alter_table_type();
    }

    public function down() {
		
    }
}