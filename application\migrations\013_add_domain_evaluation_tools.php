<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Add_domain_evaluation_tools extends CI_Migration {

    public function up()
    {
        // Add new domain evaluation tools settings
        $new_options = array(
            // Tool Status Settings
            array(
                'name' => 'geo-domain-status',
                'value' => '1',
                'autoload' => 1
            ),
            array(
                'name' => 'exact-match-status',
                'value' => '1',
                'autoload' => 1
            ),
            array(
                'name' => 'brandable-status',
                'value' => '1',
                'autoload' => 1
            ),
            array(
                'name' => 'keyword-status',
                'value' => '1',
                'autoload' => 1
            ),
            
            // SEO Settings for Geo Domain Checker
            array(
                'name' => 'seo-geo-domain-title',
                'value' => 'Geo Domain Checker',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-geo-domain-keywords',
                'value' => 'Geo Domain Checker, Geographic Domain Value, Domain Location Value, City Domain Checker, Local Domain Evaluation',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-geo-domain-description',
                'value' => 'Evaluate geographic domain value and commercial potential. Check if your domain contains valuable city names and geographic keywords.',
                'autoload' => 1
            ),
            
            // SEO Settings for Exact Match Checker
            array(
                'name' => 'seo-exact-match-title',
                'value' => 'Exact Match Domain Checker',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-exact-match-keywords',
                'value' => 'Exact Match Domain, EMD Checker, Keyword Domain Value, SEO Domain Value, Commercial Domain Keywords',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-exact-match-description',
                'value' => 'Analyze exact match keywords and commercial value potential. Check if your domain contains high-value commercial keywords for SEO and PPC.',
                'autoload' => 1
            ),
            
            // SEO Settings for Brandable Checker
            array(
                'name' => 'seo-brandable-title',
                'value' => 'Brandable Domain Checker',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-brandable-keywords',
                'value' => 'Brandable Domain, Brand Domain Value, Memorable Domain, Pronounceable Domain, Unique Domain Names',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-brandable-description',
                'value' => 'Evaluate domain brandability, memorability, and business potential. Check if your domain name is suitable for building a strong brand.',
                'autoload' => 1
            ),
            
            // SEO Settings for Keyword Checker
            array(
                'name' => 'seo-keyword-title',
                'value' => 'Keyword Domain Checker',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-keyword-keywords',
                'value' => 'Keyword Domain Checker, Domain Keyword Analysis, SEO Domain Value, Search Volume Domain, PPC Domain Value',
                'autoload' => 1
            ),
            array(
                'name' => 'seo-keyword-description',
                'value' => 'Analyze keyword potential, search volume, and SEO/PPC opportunities. Check if your domain contains valuable keywords for search marketing.',
                'autoload' => 1
            )
        );

        // Insert new options into database
        foreach ($new_options as $option) {
            // Check if option already exists
            $existing = $this->db->get_where('options', array('name' => $option['name']))->row();
            
            if (!$existing) {
                $this->db->insert('options', $option);
            }
        }
        
        // Clear options cache if it exists
        if (file_exists(APPPATH . 'cache/options')) {
            unlink(APPPATH . 'cache/options');
        }
    }

    public function down()
    {
        // Remove the new options
        $option_names = array(
            'geo-domain-status',
            'exact-match-status', 
            'brandable-status',
            'keyword-status',
            'seo-geo-domain-title',
            'seo-geo-domain-keywords',
            'seo-geo-domain-description',
            'seo-exact-match-title',
            'seo-exact-match-keywords',
            'seo-exact-match-description',
            'seo-brandable-title',
            'seo-brandable-keywords',
            'seo-brandable-description',
            'seo-keyword-title',
            'seo-keyword-keywords',
            'seo-keyword-description'
        );
        
        foreach ($option_names as $name) {
            $this->db->delete('options', array('name' => $name));
        }
        
        // Clear options cache if it exists
        if (file_exists(APPPATH . 'cache/options')) {
            unlink(APPPATH . 'cache/options');
        }
    }
}
