body{
	overflow-x:hidden;
	background:#f7f7f7;
	font-size:14px;
}
html, body{
	height:100%;
}
.img-responsivee{ max-width:100%;}
body, p, form, input, h1, h2, h3, h4, h5, h6, p, form, ul, li, ol, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section { font-family: 'Ubuntu'; color:#555555;}
a{ color:#4F46E5; text-decoration:none; font-family: 'Ubuntu'; }
a:hover, a:focus, a:active { text-decoration:none; color:#1e6db7;}

button, a, input, textarea{
	outline:0 !important;
}

/* Margin */
.m-all-0{ margin:0px !important;} .m-all-10{ margin:10px !important;} .m-all-20{ margin:20px !important;} .m-all-30{ margin:30px !important;} .m-all-40{ margin:40px !important;}

.m-l-0{ margin-left:0px !important;} .m-r-0{ margin-right:0px !important;} .m-t-0{ margin-top:0px !important;} .m-b-0{ margin-bottom:0px !important;}

.m-l-1{ margin-left:1px !important;} .m-l-2{ margin-left:2px !important;} .m-l-3{ margin-left:3px !important;} .m-l-4{ margin-left:4px !important;} .m-l-5{ margin-left:5px !important;} .m-l-6{ margin-left:6px !important;} .m-l-7{ margin-left:7px !important;} .m-l-8{ margin-left:8px !important;} .m-l-9{ margin-left:9px !important;}

.m-r-1{ margin-right:1px !important;} .m-r-2{ margin-right:2px !important;} .m-r-3{ margin-right:3px !important;} .m-r-4{ margin-right:4px !important;} .m-r-5{ margin-right:5px !important;} .m-r-6{ margin-right:6px !important;} .m-r-7{ margin-right:7px !important;} .m-r-8{ margin-right:8px !important;} .m-r-9{ margin-right:9px !important;}

.m-t-1{ margin-top:1px !important;} .m-t-2{ margin-top:2px !important;} .m-t-3{ margin-top:3px !important;} .m-t-4{ margin-top:4px !important;} .m-t-5{ margin-top:5px !important;} .m-t-6{ margin-top:6px !important;} .m-t-7{ margin-top:7px !important;} .m-t-8{ margin-top:8px !important;} .m-t-9{ margin-top:9px !important;}

.m-b-1{ margin-bottom:1px !important;} .m-b-2{ margin-bottom:2px !important;} .m-b-3{ margin-bottom:3px !important;} .m-b-4{ margin-bottom:4px !important;} .m-b-5{ margin-bottom:5px !important;} .m-b-6{ margin-bottom:6px !important;} .m-b-7{ margin-bottom:7px !important;} .m-b-8{ margin-bottom:8px !important;} .m-b-9{ margin-bottom:9px !important;}

.m-l-10{ margin-left:10px !important;} .m-r-10{ margin-right:10px !important;} .m-t-10{ margin-top:10px !important;} .m-b-10{ margin-bottom:10px !important;}  .m-l-15{ margin-left:15px !important;} .m-r-15{ margin-right:15px !important;} .m-t-15{ margin-top:15px !important;} .m-b-15{ margin-bottom:15px !important;}

.m-l-20{ margin-left:20px !important;} .m-r-20{ margin-right:20px !important;} .m-t-20{ margin-top:20px !important;} .m-b-20{ margin-bottom:20px !important;}

.m-l-25{ margin-left:25px !important;} .m-r-25{ margin-right:25px !important;} .m-t-25{ margin-top:25px !important;} .m-b-25{ margin-bottom:25px !important;}

.m-l-30{ margin-left:30px !important;} .m-r-30{ margin-right:30px !important;} .m-t-30{ margin-top:30px !important;} .m-b-30{ margin-bottom:30px !important;}

.m-l-35{ margin-left:35px !important;} .m-r-35{ margin-right:35px !important;} .m-t-35{ margin-top:35px !important;} .m-b-35{ margin-bottom:35px !important;}

.m-l-40{ margin-left:40px !important;} .m-r-40{ margin-right:40px !important;} .m-t-40{ margin-top:40px !important;} .m-b-40{ margin-bottom:40px !important;}

.m-l-45{ margin-left:45px !important;} .m-r-45{ margin-right:45px !important;} .m-t-45{ margin-top:45px !important;} .m-b-45{ margin-bottom:45px !important;}

.m-l-50{ margin-left:40px !important;} .m-r-50{ margin-right:40px !important;} .m-t-50{ margin-top:40px !important;} .m-b-50{ margin-bottom:40px !important;}

/* Padding */

.p-all-0{ padding:0px !important;} .p-all-10{ padding:10px !important;} .p-all-20{ padding:20px !important;} .p-all-30{ padding:30px !important;} .p-all-40{ padding:40px !important;}

.p-l-0{ padding-left:0px !important;} .p-r-0{ padding-right:0px !important;} .p-t-0{ padding-top:0px !important;} .p-b-0{ padding-bottom:0px !important;}

.p-l-1{ padding-left:1px !important;} .p-l-2{ padding-left:2px !important;} .p-l-3{ padding-left:3px !important;} .p-l-4{ padding-left:4px !important;} .p-l-5{ padding-left:5px !important;} .p-l-6{ padding-left:6px !important;} .p-l-7{ padding-left:7px !important;} .p-l-8{ padding-left:8px !important;} .p-l-9{ padding-left:9px !important;}

.p-r-1{ padding-right:1px !important;} .p-r-2{ padding-right:2px !important;} .p-r-3{ padding-right:3px !important;} .p-r-4{ padding-right:4px !important;} .p-r-5{ padding-right:5px !important;} .p-r-6{ padding-right:6px !important;} .p-r-7{ padding-right:7px !important;} .p-r-8{ padding-right:8px !important;} .p-r-9{ padding-right:9px !important;}

.p-t-1{ padding-top:1px !important;} .p-t-2{ padding-top:2px !important;} .p-t-3{ padding-top:3px !important;} .p-t-4{ padding-top:4px !important;} .p-t-5{ padding-top:5px !important;} .p-t-6{ padding-top:6px !important;} .p-t-7{ padding-top:7px !important;} .p-t-8{ padding-top:8px !important;} .p-t-9{ padding-top:9px !important;}

.p-b-1{ padding-bottom:1px !important;} .p-b-2{ padding-bottom:2px !important;} .p-b-3{ padding-bottom:3px !important;} .p-b-4{ padding-bottom:4px !important;} .p-b-5{ padding-bottom:5px !important;} .p-b-6{ padding-bottom:6px !important;} .p-b-7{ padding-bottom:7px !important;} .p-b-8{ padding-bottom:8px !important;} .p-b-9{ padding-bottom:9px !important;}

.p-l-10{ padding-left:10px !important;} .p-r-10{ padding-right:10px !important;} .p-t-10{ padding-top:10px !important;} .p-b-10{ padding-bottom:10px !important;}

.p-l-15{ padding-left:15px !important;} .p-r-15{ padding-right:15px !important;} .p-t-15{ padding-top:15px !important;} .p-b-15{ padding-bottom:15px !important;}

.p-l-20{ padding-left:20px !important;} .p-r-20{ padding-right:20px !important;} .p-t-20{ padding-top:20px !important;} .p-b-20{ padding-bottom:20px !important;}

.p-l-25{ padding-left:25px !important;} .p-r-25{ padding-right:25px !important;} .p-t-25{ padding-top:25px !important;} .p-b-25{ padding-bottom:25px !important;}

.p-l-30{ padding-left:30px !important;} .p-r-30{ padding-right:30px !important;} .p-t-30{ padding-top:30px !important;} .p-b-30{ padding-bottom:30px !important;}

.p-l-35{ padding-left:35px !important;} .p-r-35{ padding-right:35px !important;} .p-t-35{ padding-top:35px !important;} .p-b-35{ padding-bottom:35px !important;}

.p-l-40{ padding-left:40px !important;} .p-r-40{ padding-right:40px !important;} .p-t-40{ padding-top:40px !important;} .p-b-40{ padding-bottom:40px !important;}

.p-l-45{ padding-left:45px !important;} .p-r-45{ padding-right:45px !important;} .p-t-45{ padding-top:45px !important;} .p-b-45{ padding-bottom:45px !important;}

.p-l-50{ padding-left:50px !important;} .p-r-50{ padding-right:50px !important;} .p-t-50{ padding-top:50px !important;} .p-b-50{ padding-bottom:50px !important;}

.border-left-none{ border-left:none !important;} .border-right-none{ border-right:none !important;} .border-top-none{ border-top:none !important;} .border-bottom-none{ border-bottom:none !important;} .border-all-none{ border:none !important;}


.form-control, .btn.focus, .btn:focus, .custom-select:focus, .form-control:focus, .custom-control-input:focus ~ .custom-control-label:before{
	box-shadow:none;
}
.form-control:focus {
	border-color: #4F46E5;
}



.installer-container{
	max-width:705px;
	margin:0 auto;
	padding:120px 15px;
}
.installer-logo-area{
	text-align:center;
	color:#466282;
	font-size:45px;
	text-transform:uppercase;
	font-weight:700;
}
.installer-logo-area .logo-icon{
	display: block;
}
.installer-logo-area .logo-icon:hover{
}
.welcome-logo-text{
	background:#466282;
	border-radius:5px;
	text-shadow:1px 1px 1px #092443;
	font-size: 18px;
	color:#fff;
	font-weight:300;
	line-height:43px;
	margin:25px 0;
	text-align:center;
	padding: 0 55px;
	display:inline-block;
}
.welcome-logo-text span{
	font-weight:700;
}

/* Prev Color: #4F46E5 */

.thankyou-area{
	background:#f5fffd;
	border:#dbe7e5 1px solid;
	border-bottom:#4F46E5 4px solid;
	border-radius:5px;
	padding:20px;
}
.thankyou-title{
	font-size:20px;
	color:#4F46E5;
}
.thankyou-w-installation{
	font-size:14px;
	color:#6e827e;
	margin: 15px 0 10px 0;
}
.thankyou-w-support{
	font-size:14px;
	color:#80908d;
}

.tabs-area{
	border-radius:5px;
	padding:25px;
	box-shadow:0 5px 18px #d8d8d8;
	margin-top:40px;
	background:#fff;
}
.tabs-nav{
	display:flex;
	margin-bottom:25px;
	overflow-y: auto;
}
.tabs-nav-inner{
	display:flex;
	min-width: 544px;
	width: 100%;
}
.tabs-links{
	font-size:16px;
	color:#7b7b7b;
	line-height:45px;
	border-bottom:#dbdbdb 2px solid;
	text-align:center;
	flex: auto;
}
.tabs-links i{
	font-size: 14px;
	margin-right: 5px;
}
.tabs-links.active{
	font-weight:700;
	color:#4F46E5;
	border-bottom:#4F46E5 2px solid;
}
.tabs-links.complete{
	color:#466282;
	font-weight:400;
}

.tabs-content{
	
}
.tab-requirements{
	background:#439e52;
	line-height:38px;
	font-size:16px;
	color:#000;
	padding:0 20px;
	border-radius:5px;
	margin-bottom:15px;
}
.tab-requirements.inactive{
	background:#c85555;
}
.tab-requirements span{
	font-size:14px;
	color:#fff;
	margin-left:15px;
}
.tab-requirements span i{
	margin-right: 4px;
}
.tab-button-area{
	border-top:#dbdbdb 1px solid;
	padding:23px 0 0 0;
	margin-top:18px;
}
.btn-tabs{
	background:#4F46E5;
	border-radius:3px;
	font-size:18px;
	color:#fff;
	padding:0 40px;
	line-height:38px;
	border:none;
}
.btn-tabs:hover{
	background: #0d6fcc;
	color: #fff;
}
.btn-tabs:focus {
	color: #fff;
}
.footer-text{
	font-weight:300;
	font-size:16px;
	color:#535d5b;
	text-align:center;
	padding:30px 0 0 0;
}
.label-tabs{
	font-size:16px;
	color:#344456;
	font-weight:700;
}
.text-fld-div{
	padding-bottom:15px;
}
.text-fld{
	box-shadow:0 5px 18px #d8d8d8;
	border:#e8e8e8 1px solid;
	padding: 8px .75rem;
}
.installed{
	background:#45d069;
	border-radius:5px;
	display:flex;
	margin-bottom: 20px;
}
.installed .left-thumb{
	color:#fff;
	font-size:25px;
	line-height:54px;
	padding:0 20px;
	text-shadow:1px 1px 1px #34a94a;
	background:#3bc05d;
	float:left;
	border-radius:5px 0 0 5px;
}
.installed .right-thumb{
	font-size:18px;
	color:#fff;
	line-height:54px;
	text-shadow:1px 1px 1px #208f35;
	padding-left: 20px;
}


/* social */
.main-social-ul {
	list-style: none;
	margin:0;
	padding: 70px 0 0 0;
	text-align:center;
}
.main-social-li {
    margin: 0 10px;
    display: inline-block;
    width: 100px;
    text-align: center;
}
.main-social-li i{
	font-size: 33px;
	margin-bottom:5px;
}
.main-social-li i.fa-facebook-f{
	color:#364f8f;
}
.main-social-li i.fa-twitter{
	color:#02a8ea;
}
.main-social-li i.fa-google-plus-g{
	color:#db4437;
}

.main-social-li a > div {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 100px;
    text-align: center;
    border-radius: 4px;
}



.main-social-li a {
    color: #A8A8A8;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 14px;
    font-family: Arial, sans-serif;
}

.main-social-li .layer2 {
    position: absolute;
    top: 0px;
    left: -10px;
    width: 100px;
    height: 100px;
    background: white;
    border-radius: 3px;
    -webkit-box-shadow: 0 0 25px 0 rgba(00, 00, 00, 0.15);
    box-shadow: 0 0 25px 0 rgba(00, 00, 00, 0.15);
    -webkit-transition: all 0.5s ease-out;
    -moz-transition: all 0.5s ease-out;
    -ms-transition: all 0.5s ease-out;
    -o-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
	z-index:1;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}
/* hover layer effects */
.main-social-li a:hover .layer2 {
    top: -33px;
    left: -10px;
    width: 100px;
}
/* Layer content */
svg {
    height: 42px;
}
.nb {
    font-size: 15px;
}
.main-social-li .textlink {
    position: absolute;
    bottom: 6px;
    right: 10px;
    left: 10px;
    color: white;
    font-size: 12px;
    text-align: center;
	font-weight:700;
}
/* Facebook */
#fb {
    background: #364F8F;
}
/* Twitter */
#tw {
    background: #02A8EA;
}
/* Instagram */
#gplus {
    background: #db4437;
}
.btn-submit {
min-width: 129px;
}
@media screen and (max-width: 992px) {
	
}