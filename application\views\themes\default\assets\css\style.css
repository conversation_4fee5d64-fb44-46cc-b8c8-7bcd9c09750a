*, body {
  font-family: "Open Sans", sans-serif;
}

body {
  overflow-x: hidden;
  background: #F6F6F6;
  font-size: 14px;
}

html, body {
  height: 100%;
}

body, p, form, input, h1, h2, h3, h4, h5, h6, p, form, ul, li, ol, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  font-family: "Open Sans", sans-serif;
  color: #0072CF;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

a {
  color: #0072CF;
  text-decoration: none;
  font-family: "Open Sans", sans-serif;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

a:hover, a:focus, a:active {
  text-decoration: none;
  color: #000;
}

button, a, input, textarea {
  outline: 0 !important;
}

/* Margin */
.m-all-0 {
  margin: 0px !important;
}

.m-all-10 {
  margin: 10px !important;
}

.m-all-20 {
  margin: 20px !important;
}

.m-all-30 {
  margin: 30px !important;
}

.m-all-40 {
  margin: 40px !important;
}

.m-l-0 {
  margin-left: 0px !important;
}

.m-r-0 {
  margin-right: 0px !important;
}

.m-t-0 {
  margin-top: 0px !important;
}

.m-b-0 {
  margin-bottom: 0px !important;
}

.m-l-1 {
  margin-left: 1px !important;
}

.m-l-2 {
  margin-left: 2px !important;
}

.m-l-3 {
  margin-left: 3px !important;
}

.m-l-4 {
  margin-left: 4px !important;
}

.m-l-5 {
  margin-left: 5px !important;
}

.m-l-6 {
  margin-left: 6px !important;
}

.m-l-7 {
  margin-left: 7px !important;
}

.m-l-8 {
  margin-left: 8px !important;
}

.m-l-9 {
  margin-left: 9px !important;
}

.m-r-1 {
  margin-right: 1px !important;
}

.m-r-2 {
  margin-right: 2px !important;
}

.m-r-3 {
  margin-right: 3px !important;
}

.m-r-4 {
  margin-right: 4px !important;
}

.m-r-5 {
  margin-right: 5px !important;
}

.m-r-6 {
  margin-right: 6px !important;
}

.m-r-7 {
  margin-right: 7px !important;
}

.m-r-8 {
  margin-right: 8px !important;
}

.m-r-9 {
  margin-right: 9px !important;
}

.m-t-1 {
  margin-top: 1px !important;
}

.m-t-2 {
  margin-top: 2px !important;
}

.m-t-3 {
  margin-top: 3px !important;
}

.m-t-4 {
  margin-top: 4px !important;
}

.m-t-5 {
  margin-top: 5px !important;
}

.m-t-6 {
  margin-top: 6px !important;
}

.m-t-7 {
  margin-top: 7px !important;
}

.m-t-8 {
  margin-top: 8px !important;
}

.m-t-9 {
  margin-top: 9px !important;
}

.m-b-1 {
  margin-bottom: 1px !important;
}

.m-b-2 {
  margin-bottom: 2px !important;
}

.m-b-3 {
  margin-bottom: 3px !important;
}

.m-b-4 {
  margin-bottom: 4px !important;
}

.m-b-5 {
  margin-bottom: 5px !important;
}

.m-b-6 {
  margin-bottom: 6px !important;
}

.m-b-7 {
  margin-bottom: 7px !important;
}

.m-b-8 {
  margin-bottom: 8px !important;
}

.m-b-9 {
  margin-bottom: 9px !important;
}

.m-l-10 {
  margin-left: 10px !important;
}

.m-r-10 {
  margin-right: 10px !important;
}

.m-t-10 {
  margin-top: 10px !important;
}

.m-b-10 {
  margin-bottom: 10px !important;
}

.m-l-15 {
  margin-left: 15px !important;
}

.m-r-15 {
  margin-right: 15px !important;
}

.m-t-15 {
  margin-top: 15px !important;
}

.m-b-15 {
  margin-bottom: 15px !important;
}

.m-l-20 {
  margin-left: 20px !important;
}

.m-r-20 {
  margin-right: 20px !important;
}

.m-t-20 {
  margin-top: 20px !important;
}

.m-b-20 {
  margin-bottom: 20px !important;
}

.m-l-25 {
  margin-left: 25px !important;
}

.m-r-25 {
  margin-right: 25px !important;
}

.m-t-25 {
  margin-top: 25px !important;
}

.m-b-25 {
  margin-bottom: 25px !important;
}

.m-l-30 {
  margin-left: 30px !important;
}

.m-r-30 {
  margin-right: 30px !important;
}

.m-t-30 {
  margin-top: 30px !important;
}

.m-b-30 {
  margin-bottom: 30px !important;
}

.m-l-35 {
  margin-left: 35px !important;
}

.m-r-35 {
  margin-right: 35px !important;
}

.m-t-35 {
  margin-top: 35px !important;
}

.m-b-35 {
  margin-bottom: 35px !important;
}

.m-l-40 {
  margin-left: 40px !important;
}

.m-r-40 {
  margin-right: 40px !important;
}

.m-t-40 {
  margin-top: 40px !important;
}

.m-b-40 {
  margin-bottom: 40px !important;
}

.m-l-45 {
  margin-left: 45px !important;
}

.m-r-45 {
  margin-right: 45px !important;
}

.m-t-45 {
  margin-top: 45px !important;
}

.m-b-45 {
  margin-bottom: 45px !important;
}

.m-l-50 {
  margin-left: 40px !important;
}

.m-r-50 {
  margin-right: 40px !important;
}

.m-t-50 {
  margin-top: 40px !important;
}

.m-b-50 {
  margin-bottom: 40px !important;
}

/* Padding */
.p-all-0 {
  padding: 0px !important;
}

.p-all-10 {
  padding: 10px !important;
}

.p-all-20 {
  padding: 20px !important;
}

.p-all-30 {
  padding: 30px !important;
}

.p-all-40 {
  padding: 40px !important;
}

.p-l-0 {
  padding-left: 0px !important;
}

.p-r-0 {
  padding-right: 0px !important;
}

.p-t-0 {
  padding-top: 0px !important;
}

.p-b-0 {
  padding-bottom: 0px !important;
}

.p-l-1 {
  padding-left: 1px !important;
}

.p-l-2 {
  padding-left: 2px !important;
}

.p-l-3 {
  padding-left: 3px !important;
}

.p-l-4 {
  padding-left: 4px !important;
}

.p-l-5 {
  padding-left: 5px !important;
}

.p-l-6 {
  padding-left: 6px !important;
}

.p-l-7 {
  padding-left: 7px !important;
}

.p-l-8 {
  padding-left: 8px !important;
}

.p-l-9 {
  padding-left: 9px !important;
}

.p-r-1 {
  padding-right: 1px !important;
}

.p-r-2 {
  padding-right: 2px !important;
}

.p-r-3 {
  padding-right: 3px !important;
}

.p-r-4 {
  padding-right: 4px !important;
}

.p-r-5 {
  padding-right: 5px !important;
}

.p-r-6 {
  padding-right: 6px !important;
}

.p-r-7 {
  padding-right: 7px !important;
}

.p-r-8 {
  padding-right: 8px !important;
}

.p-r-9 {
  padding-right: 9px !important;
}

.p-t-1 {
  padding-top: 1px !important;
}

.p-t-2 {
  padding-top: 2px !important;
}

.p-t-3 {
  padding-top: 3px !important;
}

.p-t-4 {
  padding-top: 4px !important;
}

.p-t-5 {
  padding-top: 5px !important;
}

.p-t-6 {
  padding-top: 6px !important;
}

.p-t-7 {
  padding-top: 7px !important;
}

.p-t-8 {
  padding-top: 8px !important;
}

.p-t-9 {
  padding-top: 9px !important;
}

.p-b-1 {
  padding-bottom: 1px !important;
}

.p-b-2 {
  padding-bottom: 2px !important;
}

.p-b-3 {
  padding-bottom: 3px !important;
}

.p-b-4 {
  padding-bottom: 4px !important;
}

.p-b-5 {
  padding-bottom: 5px !important;
}

.p-b-6 {
  padding-bottom: 6px !important;
}

.p-b-7 {
  padding-bottom: 7px !important;
}

.p-b-8 {
  padding-bottom: 8px !important;
}

.p-b-9 {
  padding-bottom: 9px !important;
}

.p-l-10 {
  padding-left: 10px !important;
}

.p-r-10 {
  padding-right: 10px !important;
}

.p-t-10 {
  padding-top: 10px !important;
}

.p-b-10 {
  padding-bottom: 10px !important;
}

.p-l-15 {
  padding-left: 15px !important;
}

.p-r-15 {
  padding-right: 15px !important;
}

.p-t-15 {
  padding-top: 15px !important;
}

.p-b-15 {
  padding-bottom: 15px !important;
}

.p-l-20 {
  padding-left: 20px !important;
}

.p-r-20 {
  padding-right: 20px !important;
}

.p-t-20 {
  padding-top: 20px !important;
}

.p-b-20 {
  padding-bottom: 20px !important;
}

.p-l-25 {
  padding-left: 25px !important;
}

.p-r-25 {
  padding-right: 25px !important;
}

.p-t-25 {
  padding-top: 25px !important;
}

.p-b-25 {
  padding-bottom: 25px !important;
}

.p-l-30 {
  padding-left: 30px !important;
}

.p-r-30 {
  padding-right: 30px !important;
}

.p-t-30 {
  padding-top: 30px !important;
}

.p-b-30 {
  padding-bottom: 30px !important;
}

.p-l-35 {
  padding-left: 35px !important;
}

.p-r-35 {
  padding-right: 35px !important;
}

.p-t-35 {
  padding-top: 35px !important;
}

.p-b-35 {
  padding-bottom: 35px !important;
}

.p-l-40 {
  padding-left: 40px !important;
}

.p-r-40 {
  padding-right: 40px !important;
}

.p-t-40 {
  padding-top: 40px !important;
}

.p-b-40 {
  padding-bottom: 40px !important;
}

.p-l-45 {
  padding-left: 45px !important;
}

.p-r-45 {
  padding-right: 45px !important;
}

.p-t-45 {
  padding-top: 45px !important;
}

.p-b-45 {
  padding-bottom: 45px !important;
}

.p-l-50 {
  padding-left: 50px !important;
}

.p-r-50 {
  padding-right: 50px !important;
}

.p-t-50 {
  padding-top: 50px !important;
}

.p-b-50 {
  padding-bottom: 50px !important;
}

.border-left-none {
  border-left: none !important;
}

.border-right-none {
  border-right: none !important;
}

.border-top-none {
  border-top: none !important;
}

.border-bottom-none {
  border-bottom: none !important;
}

.border-all-none {
  border: none !important;
}

.cursor-pointer {
  cursor: pointer;
}

.form-control, .btn.focus, .btn:focus, .custom-select:focus, .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

label {
  color: #757575;
  font-weight: 600;
  font-size: 18px;
}

label.custom-control-label {
  color: #757575;
  font-weight: 400;
  font-size: 15px;
}

.custom-control-input:focus ~ .custom-control-label:before {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.customInput {
  height: 50px;
  font-weight: 300;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 18px;
}

.customInput.placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customInput:-moz-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customInput::-moz-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customInput:-ms-input-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customInput::-webkit-input-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customTextarea {
  font-weight: 300;
  padding: 12px 20px;
  font-size: 18px;
}

.customTextarea.placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customTextarea:-moz-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customTextarea::-moz-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customTextarea:-ms-input-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.customTextarea::-webkit-input-placeholder {
  color: #f8f8f8;
  opacity: 1;
}

.form-control:focus {
  border-color: #69bcff;
}

.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.positionRelative {
  position: relative;
}

.img-responsivee {
  max-width: 100%;
  height: auto;
}

.container {
  position: relative;
}

.mainSection {
  min-height: 100%;
  position: relative;
  overflow: hidden;
}

.mainSection::before {
  content: '';
  position: absolute;
  z-index: -1;
  left: -170px;
  top: 260px;
  width: 640px;
  height: 639px;
  background: url(../images/leftbg.svg) center center no-repeat;
  background-size: 100% 100%;
}

.mainSection::after {
  content: '';
  position: absolute;
  z-index: -1;
  right: -170px;
  bottom: 80px;
  width: 597px;
  height: 536px;
  background: url(../images/leftbg.svg) center center no-repeat;
  background-size: 100% 100%;
}

.mainPadding {
  padding-left: 140px !important;
  padding-right: 140px !important;
}

@media only screen and (max-width: 1399px) {
  .mainPadding {
    padding-left: 50px !important;
    padding-right: 50px !important;
  }
}

@media only screen and (max-width: 767px) {
  .mainPadding {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.toggle-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.toggle-slot {
  position: relative;
  width: 56px;
  height: 30px;
  border: 1px solid white;
  border-radius: 100px;
  background-color: white;
  -webkit-box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
  -webkit-transition: background-color 250ms;
  transition: background-color 250ms;
}

.toggle-checkbox:checked ~ .toggle-slot {
  background-color: #374151;
  border-color: #374151;
}

.toggle-button {
  -webkit-transform: translate(29px, 3px);
          transform: translate(29px, 3px);
  position: absolute;
  height: 22px;
  width: 22px;
  border-radius: 50%;
  background-color: #ffdaa3;
  -webkit-box-shadow: inset 0px 0px 0px 3px #fdb23e;
          box-shadow: inset 0px 0px 0px 3px #fdb23e;
  -webkit-transition: background-color 250ms, border-color 250ms, -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: background-color 250ms, border-color 250ms, -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: background-color 250ms, border-color 250ms, transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: background-color 250ms, border-color 250ms, transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71), -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
}

.toggle-checkbox:checked ~ .toggle-slot .toggle-button {
  background-color: #485367;
  -webkit-box-shadow: inset 0px 0px 0px 3px white;
          box-shadow: inset 0px 0px 0px 3px white;
  -webkit-transform: translate(3px, 3px);
          transform: translate(3px, 3px);
}

.sun-icon-wrapper {
  position: absolute;
  height: 20px;
  width: 20px;
  opacity: 1;
  -webkit-transform: translate(6px, -2px) rotate(15deg);
          transform: translate(6px, -2px) rotate(15deg);
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
  -webkit-transition: opacity 150ms, -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: opacity 150ms, -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: opacity 150ms, transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
  transition: opacity 150ms, transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71), -webkit-transform 500ms cubic-bezier(0.26, 2, 0.46, 0.71);
}

.toggle-checkbox:checked ~ .toggle-slot .sun-icon-wrapper {
  opacity: 0;
  -webkit-transform: translate(4px, -2px) rotate(0deg);
          transform: translate(4px, -2px) rotate(0deg);
}

.moon-icon-wrapper {
  position: absolute;
  height: 18px;
  width: 18px;
  opacity: 0;
  -webkit-transform: translate(28px, -2px) rotate(0deg);
          transform: translate(28px, -2px) rotate(0deg);
  -webkit-transform-origin: 50% 50%;
          transform-origin: 50% 50%;
  -webkit-transition: opacity 150ms, -webkit-transform 500ms cubic-bezier(0.26, 2.5, 0.46, 0.71);
  transition: opacity 150ms, -webkit-transform 500ms cubic-bezier(0.26, 2.5, 0.46, 0.71);
  transition: opacity 150ms, transform 500ms cubic-bezier(0.26, 2.5, 0.46, 0.71);
  transition: opacity 150ms, transform 500ms cubic-bezier(0.26, 2.5, 0.46, 0.71), -webkit-transform 500ms cubic-bezier(0.26, 2.5, 0.46, 0.71);
}

.toggle-checkbox:checked ~ .toggle-slot .moon-icon-wrapper {
  opacity: 1;
  -webkit-transform: translate(30px, -2px) rotate(-15deg);
          transform: translate(30px, -2px) rotate(-15deg);
}

header {
  position: relative;
  z-index: 2;
  background: url(../images/headerGradient.svg) center top no-repeat;
  height: 153px;
  padding: 43px 0 0 0;
  margin-bottom: 66px;
}

@media only screen and (min-width: 1200px) {
  header {
    background-size: cover;
    background-position: center bottom;
  }
}

@media only screen and (max-width: 991px) {
  header {
    height: 74px;
    padding: 15px 0 0 0;
    margin-bottom: 25px;
  }
}

@media only screen and (max-width: 991px) {
  header nav .navbarMob {
    border-radius: 4px;
    background: #fff;
    -webkit-box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
            box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);
    margin-top: 7px;
    position: absolute;
    top: 45px;
    width: 100%;
  }
}

header .navbar-brand {
  max-width: 190px;
}

header .navbar-dark .headerNavigation {
  margin-left: auto;
}

@media only screen and (max-width: 991px) {
  header .navbar-dark .headerNavigation {
    padding: 12px 0;
  }
}

header .navbar-dark .headerNavigation li a.nav-link {
  font-size: 21px;
  color: #fff;
  padding: 0 16px;
}

@media only screen and (max-width: 991px) {
  header .navbar-dark .headerNavigation li a.nav-link {
    color: #000;
    font-size: 16px;
    line-height: 32px;
  }
}

@media only screen and (max-width: 991px) {
  header .navbar-dark .headerNavigation li:hover a.nav-link, header .navbar-dark .headerNavigation li.active a.nav-link {
    color: #0072CF;
  }
}

.blockGrad {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  background: #008CFF;
  background: -webkit-gradient(linear, left top, right top, from(#008CFF), to(#0072CF));
  background: linear-gradient(to right, #008CFF 0%, #0072CF 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$gradBlueLight', endColorstr='$gradBlueDark',GradientType=1 );
}

.headerBottom {
  position: relative;
  z-index: 1;
  padding: 0 0 44px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: initial;
      -ms-flex-align: initial;
          align-items: initial;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

@media only screen and (max-width: 1399px) {
  .headerBottom {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

@media only screen and (max-width: 991px) {
  .headerBottom {
    padding-bottom: 30px;
  }
}

@media only screen and (max-width: 767px) {
  .headerBottom {
    padding-bottom: 22px;
  }
}

.headerBottom .headerBottomBlocks {
  position: relative;
  overflow: hidden;
  background: #fff;
  margin-right: 35px;
  border-radius: 4px;
  padding: 15px 12px 20px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

@media only screen and (max-width: 1399px) {
  .headerBottom .headerBottomBlocks {
    -webkit-box-flex: 1;
        -ms-flex: auto;
            flex: auto;
    margin: 0 8px 15px 8px;
  }
}

@media only screen and (max-width: 991px) {
  .headerBottom .headerBottomBlocks {
    padding: 15px 12px 15px;
    margin-right: 15px;
  }
}

@media only screen and (max-width: 767px) {
  .headerBottom .headerBottomBlocks {
    margin-right: 5px;
    padding: 10px 0 10px;
    -webkit-box-shadow: 0px 9px 10px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 9px 10px rgba(0, 0, 0, 0.1);
  }
}

.headerBottom .headerBottomBlocks:last-child {
  margin-right: 0;
}

.headerBottom .headerBottomBlocks .blockGrad {
  opacity: 0;
}

.headerBottom .headerBottomBlocks .headerBottomBlockIcon {
  position: relative;
  z-index: 1;
  width: 54px;
  height: 54px;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

@media only screen and (max-width: 991px) {
  .headerBottom .headerBottomBlocks .headerBottomBlockIcon {
    width: 34px;
    height: 34px;
    margin-bottom: 7px;
  }
}

@media only screen and (max-width: 767px) {
  .headerBottom .headerBottomBlocks .headerBottomBlockIcon {
    width: 34px;
    height: 34px;
    margin-bottom: 5px;
  }
}

.headerBottom .headerBottomBlocks .headerBottomBlockIcon svg {
  max-width: 100%;
  max-height: 100%;
}

.headerBottom .headerBottomBlocks .headerBottomBlockIcon svg path {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  fill: #707070;
}

.headerBottom .headerBottomBlocks .headerBottomBlockIcon svg circle {
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  fill: #707070;
}

.headerBottom .headerBottomBlocks .headerBottomBlocks-title {
  position: relative;
  z-index: 1;
  font-size: 18px;
  color: #707070;
  text-transform: uppercase;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  text-align: center;
  line-height: 1;
  margin: 0;
  font-weight: 700;
}

@media only screen and (max-width: 991px) {
  .headerBottom .headerBottomBlocks .headerBottomBlocks-title {
    font-size: 14px;
  }
}

@media only screen and (max-width: 767px) {
  .headerBottom .headerBottomBlocks .headerBottomBlocks-title {
    font-size: 9px;
  }
}

.headerBottom .headerBottomBlocks:hover .blockGrad, .headerBottom .headerBottomBlocks.active .blockGrad {
  opacity: 1;
}

.headerBottom .headerBottomBlocks:hover .headerBottomBlockIcon svg path, .headerBottom .headerBottomBlocks.active .headerBottomBlockIcon svg path {
  fill: #fff;
}

.headerBottom .headerBottomBlocks:hover .headerBottomBlockIcon svg circle, .headerBottom .headerBottomBlocks.active .headerBottomBlockIcon svg circle {
  fill: #fff;
}

.headerBottom .headerBottomBlocks:hover .headerBottomBlocks-title, .headerBottom .headerBottomBlocks.active .headerBottomBlocks-title {
  color: #fff;
}

.searchSection {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.searchSection .searchInput {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative;
}

.searchSection .searchInput .searchCross {
  cursor: pointer;
  width: 38px;
  height: 38px;
  border-radius: 100px;
  background: #ededed;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media only screen and (max-width: 767px) {
  .searchSection .searchInput .searchCross {
    right: 10px;
    width: 33px;
    height: 33px;
  }
}

@media only screen and (max-width: 767px) {
  .searchSection .searchInput .searchCross svg {
    width: 25px;
    height: 25px;
  }
}

.searchSection .inputFiled {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  padding: 15px 37px;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  height: 76px;
  border: none;
  font-size: 24px;
  color: #707070;
}

.searchSection .inputFiled::-webkit-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.searchSection .inputFiled:-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.searchSection .inputFiled::-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.searchSection .inputFiled::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.searchSection .inputFiled:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #B0B0B0;
}

.searchSection .inputFiled::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #B0B0B0;
}

@media only screen and (max-width: 991px) {
  .searchSection .inputFiled {
    height: 60px;
    padding: 10px 22px;
    font-size: 20px;
  }
}

@media only screen and (max-width: 767px) {
  .searchSection .inputFiled {
    height: 50px;
  }
}

@media only screen and (max-width: 575px) {
  .searchSection .inputFiled {
    font-size: 18px;
  }
}

.searchSection .searchButton {
  position: relative;
  width: 260px;
  height: 76px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 700;
  margin-left: 16px;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  background: #008CFF;
  color: #fff;
}

.searchSection .searchButton:hover {
  background: #008CFF;
}

.searchSection .searchButton:hover .blockGrad {
  opacity: 0;
}

.searchSection .searchButton span {
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 991px) {
  .searchSection .searchButton {
    width: 190px;
    height: 60px;
    font-size: 20px;
  }
}

@media only screen and (max-width: 767px) {
  .searchSection .searchButton {
    height: 50px;
  }
}

@media only screen and (max-width: 575px) {
  .searchSection .searchButton {
    width: 80px;
    font-size: 14px;
  }
}

.resultAvailorNotMain {
  padding: 64px 0 0 0;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain {
    padding-top: 25px;
  }
}

.resultAvailorNotMain .resultAvailorNot {
  position: relative;
  border-radius: 4px;
  z-index: 1;
}

.resultAvailorNotMain .resultAvailorNot.availabel {
  background: #00CF6E;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #00CF6E), to(#009ED9));
  background: linear-gradient(to right, #00CF6E 1%, #009ED9 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' $colorGreen', endColorstr='#009ED9',GradientType=1 );
}

.resultAvailorNotMain .resultAvailorNot.availabel .availMan {
  position: absolute;
  right: 43px;
  bottom: 0;
  width: 301px;
  height: 247px;
  background: url(../images/availableGoodMan.svg) center center no-repeat;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain .resultAvailorNot.availabel .availMan {
    display: none;
  }
}

.resultAvailorNotMain .resultAvailorNot.availabel .leftSec:before {
  content: '';
  left: -21px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  height: 162px;
  width: 162px;
  background: url(../images/availableGood.svg) center center no-repeat;
  background-size: 100%;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain .resultAvailorNot.availabel .leftSec:before {
    width: 112px;
    height: 112px;
  }
}

.resultAvailorNotMain .resultAvailorNot.notAvailabel {
  background: #CF0031;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #CF0031), to(#3768C9));
  background: linear-gradient(to right, #CF0031 1%, #3768C9 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#3768C9',GradientType=1 );
}

.resultAvailorNotMain .resultAvailorNot.notAvailabel .availMan {
  position: absolute;
  right: 43px;
  bottom: -59px;
  width: 245px;
  height: 306px;
  background: url(../images/availableNotMan.svg) center center no-repeat;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain .resultAvailorNot.notAvailabel .availMan {
    display: none;
  }
}

.resultAvailorNotMain .resultAvailorNot.notAvailabel .leftSec:before {
  content: '';
  left: -21px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  height: 162px;
  width: 162px;
  background: url(../images/availableNot.svg) center center no-repeat;
  background-size: 100%;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain .resultAvailorNot.notAvailabel .leftSec:before {
    width: 112px;
    height: 112px;
  }
}

.resultAvailorNotMain .resultAvailorNot .leftSec {
  position: relative;
  overflow: hidden;
  padding: 65px 0 65px 50px;
}

@media only screen and (max-width: 991px) {
  .resultAvailorNotMain .resultAvailorNot .leftSec {
    padding: 25px 0 27px 30px;
  }
}

@media only screen and (max-width: 575px) {
  .resultAvailorNotMain .resultAvailorNot .leftSec {
    padding: 17px 0 17px 22px;
  }
}

.resultAvailorNotMain .resultAvailorNot .leftSec h1 {
  position: relative;
  z-index: 1;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h2 {
  font-size: 24px;
  line-height: 29px;
  font-weight: 400;
  color: #fff;
  position: relative;
  z-index: 1;
  letter-spacing: -1px;
}

@media only screen and (max-width: 575px) {
  .resultAvailorNotMain .resultAvailorNot .leftSec h2 {
    font-size: 20px;
    margin-bottom: 2px;
  }
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 {
  font-size: 38px;
  font-weight: 800;
  line-height: 46px;
  color: #fff;
  margin: 0;
  position: relative;
  z-index: 1;
  letter-spacing: -1px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media only screen and (max-width: 575px) {
  .resultAvailorNotMain .resultAvailorNot .leftSec h3 {
    font-size: 33px;
    line-height: 35px;
  }
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .domainname-text {
  word-wrap: anywhere;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .whois-btn {
  font-weight: 500;
  font-size: 18px;
  border-radius: 4px;
  border: #fff 1px solid;
  padding: 8px 15px;
  line-height: 20px;
  color: #fff;
  margin-left: 20px;
  position: relative;
  bottom: -3px;
  letter-spacing: .5px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .whois-btn:hover, .resultAvailorNotMain .resultAvailorNot .leftSec h3 .whois-btn:focus, .resultAvailorNotMain .resultAvailorNot .leftSec h3 .whois-btn:active {
  border: #f55e82 1px solid;
  background: #91113d;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-price {
  font-weight: 400;
  font-size: 30px;
  margin-left: 10px;
  letter-spacing: -2px;
  position: relative;
  bottom: -3px;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now {
  font-weight: 700;
  font-size: 18px;
  border-radius: 4px;
  padding: 10px 17px;
  line-height: 20px;
  color: #fff;
  margin-left: 20px;
  position: relative;
  bottom: -3px;
  letter-spacing: .5px;
  background: #03BE80;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  width: 120px;
  text-transform: uppercase;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #03BE80;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #03BE80), to(#00954F));
  background: linear-gradient(to right, #03BE80 1%, #00954F 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #03BE80', endColorstr='#00954F',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now span {
  position: relative;
  z-index: 1;
}

.resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now:hover::before, .resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now:focus::before, .resultAvailorNotMain .resultAvailorNot .leftSec h3 .buy-now:active::before {
  opacity: 0;
}

/* result-content */
.result-content {
  padding-bottom: 45px;
  padding-top: 42px;
}

@media only screen and (max-width: 575px) {
  .result-content {
    padding-top: 25px;
  }
}

.result-content .result-content-inner {
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

@media only screen and (max-width: 991px) {
  .result-content .result-content-inner.firstCol {
    margin-bottom: 25px;
  }
}

.result-content .result-main-title {
  height: 87px;
  background: #fff;
  border-radius: 4px 4px 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0 11px 0 30px;
}

@media only screen and (max-width: 575px) {
  .result-content .result-main-title {
    padding: 0 11px 0 15px;
    height: 57px;
  }
}

.result-content .result-main-title.enlarge {
  height: auto;
}

.result-content .result-main-title .left-title-area {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  line-height: 47px;
  font-size: 18px;
  text-transform: uppercase;
  color: #707070;
  float: left;
  font-weight: 700;
  position: relative;
}

@media only screen and (max-width: 575px) {
  .result-content .result-main-title .left-title-area {
    font-size: 16px;
  }
}

.result-content .result-main-title .left-title-area span {
  vertical-align: -2px;
  margin-right: 7px;
}

.result-content .result-main-title .left-title-area span svg {
  vertical-align: initial;
}

.result-content .result-main-title .left-title-area span svg path {
  fill: #707070;
}

.result-content .result-main-title .left-title-area span.recent-search-icon svg {
  vertical-align: inherit;
}

.result-content .result-main-title .left-title-area.enlarge {
  font-size: 27px;
  font-weight: 600;
  text-transform: capitalize;
  padding-top: 15px;
  line-height: 1.3;
}

@media only screen and (max-width: 575px) {
  .result-content .result-main-title .left-title-area.enlarge {
    font-size: 20px;
  }
}

.result-content .result-main-title .left-title-area.enlarge .date-sec {
  font-size: 14px;
  color: #000;
  margin: 8px 0 15px 0;
}

.result-content .result-main-title .left-title-area.enlarge .date-sec a {
  background-color: rgba(0, 0, 0, 0.1);
  color: #000;
  border-radius: 100px;
  padding: 1px 10px;
  font-weight: 600;
}

.result-content .result-main-title a.see-all {
  line-height: 40px;
  font-size: 16px;
  color: #707070;
  float: right;
  padding: 0 11px 0 0;
  position: relative;
}

.result-content .result-main-title a:hover.see-all {
  text-decoration: underline;
}

/* extension-area */
.extension-area ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.extension-area ul li {
  background: #fff;
  padding: 13px;
  padding-left: 30px;
}

@media only screen and (max-width: 575px) {
  .extension-area ul li {
    padding-left: 15px;
  }
}

.extension-area ul li:nth-of-type(2n+1) {
  background: #f7f7f7;
}

.extension-area ul li:last-child {
  border-radius: 0 0 4px 4px;
}

.extension-area ul li .left-extension {
  font-size: 20px;
  color: #707070;
  line-height: 37px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

@media only screen and (max-width: 575px) {
  .extension-area ul li .left-extension {
    line-height: 30px;
    font-size: 18px;
  }
}

.extension-area ul li .left-extension.green {
  color: #00CF6E;
}

.extension-area ul li .left-extension.red {
  color: #e50914;
}

.extension-area ul li .left-extension.wd-icon {
  padding-left: 35px;
  position: relative;
}

.extension-area ul li .left-extension.wd-icon .icon {
  width: 25px;
  height: 25px;
  border-radius: 4px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.extension-area ul li .left-extension.wd-icon .icon img {
  width: 25px;
  max-width: 100%;
  height: auto;
}

.extension-area ul li .right-by-wo-btn {
  border: none;
  border-radius: 4px;
  margin-left: 16px;
  padding: 0;
  width: 120px;
  height: 37px;
  line-height: 37px;
  font-size: 17px;
  font-weight: 700;
  text-transform: uppercase;
  float: right;
  background: #fff;
  text-align: center;
  color: #fff;
  position: relative;
  overflow: hidden;
}

@media only screen and (max-width: 575px) {
  .extension-area ul li .right-by-wo-btn {
    width: 80px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
}

.extension-area ul li .right-by-wo-btn span, .extension-area ul li .right-by-wo-btn svg {
  position: relative;
  z-index: 1;
}

.extension-area ul li .right-by-wo-btn:hover:before, .extension-area ul li .right-by-wo-btn:focus:before {
  opacity: 0;
}

.extension-area ul li .right-by-wo-btn.green-by-btn {
  background: #03BE80;
}

.extension-area ul li .right-by-wo-btn.green-by-btn:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #03BE80;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #03BE80), to(#00954F));
  background: linear-gradient(to right, #03BE80 1%, #00954F 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #03BE80', endColorstr='#00954F',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.extension-area ul li .right-by-wo-btn.blue-by-btn {
  background: #008CFF;
}

.extension-area ul li .right-by-wo-btn.blue-by-btn:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #008CFF;
  background: -webkit-gradient(linear, left top, right top, from(#008CFF), to(#0072CF));
  background: linear-gradient(to right, #008CFF 0%, #0072CF 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$gradBlueLight', endColorstr='$gradBlueDark',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.extension-area ul li .right-by-wo-btn.grey-by-btn {
  background: none;
}

.extension-area ul li .right-by-wo-btn.grey-by-btn:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: none;
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' lighten($colorSecondaryLight, 10%)', endColorstr='lighten($colorPlaceholder, 10%)',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.extension-area ul li .right-by-wo-btn.red-by-btn {
  background: #CF0031;
}

.extension-area ul li .right-by-wo-btn.red-by-btn:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #CF0031;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #CF0031), to(#B70932));
  background: linear-gradient(to right, #CF0031 1%, #B70932 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#B70932',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.extension-area ul li .right-price-per-yr {
  line-height: 32px;
  font-size: 14px;
  font-weight: 500;
  float: right;
  color: #707070;
}

@media only screen and (max-width: 575px) {
  .extension-area ul li .right-price-per-yr {
    line-height: 30px;
    font-size: 18px;
  }
}

.extension-area ul li .right-price-offer {
  line-height: 37px;
  font-size: 16px;
  font-weight: 500;
  float: right;
  color: #707070;
  margin-right: 6px;
}

@media only screen and (max-width: 575px) {
  .extension-area ul li .right-price-offer {
    line-height: 30px;
    font-size: 14px;
    margin-right: 0;
  }
}

.homepageColumnsArea {
  padding-top: 45px;
}

@media only screen and (max-width: 991px) {
  .homepageColumnsArea {
    padding-top: 30px;
  }
}

.homepageColumnsArea .homepageColumnsMain {
  background: #fff;
  text-align: center;
  min-height: calc(100% - 30px);
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: initial;
      -ms-flex-align: initial;
          align-items: initial;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 40px;
  margin-bottom: 30px;
}

@media only screen and (max-width: 767px) {
  .homepageColumnsArea .homepageColumnsMain {
    padding: 25px;
    -webkit-box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.1);
  }
}

.homepageColumnsArea .homepageColumnsMain .homepageColumnsIcon {
  height: 151px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: end;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin-bottom: 50px;
}

@media only screen and (max-width: 991px) {
  .homepageColumnsArea .homepageColumnsMain .homepageColumnsIcon {
    height: 120px;
    max-width: 150px;
    margin: 0 auto 40px;
  }
}

@media only screen and (max-width: 767px) {
  .homepageColumnsArea .homepageColumnsMain .homepageColumnsIcon {
    height: 100px;
    max-width: 120px;
    margin: 0 auto 25px;
  }
}

@media only screen and (max-width: 991px) {
  .homepageColumnsArea .homepageColumnsMain .homepageColumnsIcon img {
    max-height: 100%;
    max-width: 100%;
  }
}

.homepageColumnsArea .homepageColumnsMain .homepageColumnsMain-title {
  font-size: 27px;
  font-weight: 800;
  text-align: center;
  color: #000;
  text-transform: uppercase;
  min-height: 77px;
  margin: 0 0 32px 0;
}

@media only screen and (max-width: 991px) {
  .homepageColumnsArea .homepageColumnsMain .homepageColumnsMain-title {
    min-height: 55px;
    margin: 0 0 20px 0;
  }
}

@media only screen and (max-width: 767px) {
  .homepageColumnsArea .homepageColumnsMain .homepageColumnsMain-title {
    min-height: 45px;
    margin: 0 0 10px 0;
    font-size: 18px;
  }
}

.homepageColumnsArea .homepageColumnsMain p {
  font-size: 20px;
  line-height: 27px;
  text-align: center;
  color: #000;
  font-weight: 300;
  margin-bottom: 0;
}

@media only screen and (max-width: 991px) {
  .homepageColumnsArea .homepageColumnsMain p {
    font-size: 18px;
  }
}

@media only screen and (max-width: 767px) {
  .homepageColumnsArea .homepageColumnsMain p {
    font-size: 16px;
    line-height: 25px;
  }
}

.homepageFaqsAreaMain {
  padding-top: 40px;
}

@media only screen and (max-width: 767px) {
  .homepageFaqsAreaMain {
    padding-top: 10px;
  }
}

.homepageFaqsAreaMain .homepageFaqsArea {
  position: relative;
  background: #fff;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 40px;
  overflow: hidden;
}

@media only screen and (max-width: 767px) {
  .homepageFaqsAreaMain .homepageFaqsArea {
    padding: 25px;
  }
}

.homepageFaqsAreaMain .homepageFaqsArea:before {
  content: '';
  width: 982px;
  height: 980px;
  position: absolute;
  left: -210px;
  top: -660px;
  background: url(../images/leftbg.svg) center center no-repeat;
  background-size: 100%;
  opacity: .8;
}

.homepageFaqsAreaMain .homepageFaqsArea .faq-main-title {
  font-size: 65px;
  font-weight: 800;
  color: #000;
  text-align: center;
  margin-bottom: 50px;
}

.homepageFaqsAreaMain .homepageFaqsArea .faq-main-title.whoisSearchTitle {
  font-size: 48px;
}

@media only screen and (max-width: 991px) {
  .homepageFaqsAreaMain .homepageFaqsArea .faq-main-title {
    font-size: 45px;
    margin-bottom: 40px;
  }
}

@media only screen and (max-width: 767px) {
  .homepageFaqsAreaMain .homepageFaqsArea .faq-main-title {
    font-size: 40px;
    margin-bottom: 20px;
  }
}

footer {
  padding-top: 50px;
}

footer .footer-inner {
  background: #111;
  padding-bottom: 50px;
}

footer .footer-inner .navigationSection {
  padding: 50px 0;
}

@media only screen and (max-width: 767px) {
  footer .footer-inner .navigationSection {
    padding: 20px 0;
  }
}

footer .footer-inner .navigationSection .footer-nav-title {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 25px;
}

footer .footer-inner .navigationSection ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

footer .footer-inner .navigationSection ul li a {
  display: block;
  color: #fff;
  opacity: .5;
  line-height: 30px;
}

footer .footer-inner .navigationSection ul li a:hover, footer .footer-inner .navigationSection ul li a:focus {
  opacity: .8;
}

@media only screen and (max-width: 991px) {
  footer .footer-inner .navigationSection ul {
    margin-bottom: 25px;
  }
}

footer .footer-inner .navigationSection p {
  color: #fff;
  opacity: .5;
}

footer .footer-inner .navigationSection .footerLogo {
  margin-bottom: 25px;
}

footer .footer-inner .navigationSection .footerCopyright {
  max-width: 350px;
  color: #fff;
  opacity: .5;
  line-height: 25px;
}

footer .footer-inner .navigationSection .footerCopyright a {
  color: #00CF6E;
}

footer .footer-inner .navigationSection .footerCopyright a:hover, footer .footer-inner .navigationSection .footerCopyright a:focus {
  text-decoration: underline;
}

.whoisText {
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #fff;
  font-size: 15px;
  color: #000;
  line-height: 28px;
}

@media only screen and (max-width: 767px) {
  .whoisText {
    font-size: 14px;
  }
}

.domainDnsSection {
  border-top: #ebebeb 1px solid;
  background: #fff;
  padding: 30px;
}

.domainDnsSection::after {
  content: '';
  display: table;
  clear: both;
}

@media only screen and (max-width: 767px) {
  .domainDnsSection {
    padding: 20px;
  }
}

.domainDnsSection h2 {
  font-size: 20px;
  font-weight: 700;
  color: #000;
}

.domainDnsSection h3 {
  font-size: 18px;
  font-weight: 500;
  color: #000;
  margin-bottom: 20px;
}

.domainDnsSection h4 {
  font-size: 15px;
  font-weight: 700;
  color: #0072CF;
}

.domainDnsSection .dnsBlock {
  border: #ebebeb 1px solid;
  margin-bottom: 20px;
}

.domainDnsSection .dnsBlock:last-child {
  margin-bottom: none;
}

.domainDnsSection .dnsBlock .dnsBlockRow {
  padding: 10px;
  color: #000;
  border-bottom: #ebebeb 1px solid;
}

.domainDnsSection .dnsBlock .dnsBlockRow:last-child {
  border-bottom: none;
}

.contactDivider {
  border-radius: 100px;
  max-width: 500px;
  margin: 0 auto;
  background: #e6e6e6;
  height: 8px;
  margin-bottom: 40px;
}

.contactInputs {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  background: #fff;
  border-radius: 4px;
  padding: 10px 18px;
  -webkit-box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
  height: 50px;
  border: none;
  font-size: 18px;
  color: #707070;
}

.contactInputs::-webkit-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactInputs:-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactInputs::-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactInputs::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactInputs:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #B0B0B0;
}

.contactInputs::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #B0B0B0;
}

.contactInputs:focus {
  -webkit-box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
}

.contactTextarea {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  background: #fff;
  border-radius: 4px;
  padding: 10px 18px;
  -webkit-box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
  border: none;
  font-size: 18px;
  color: #707070;
}

.contactTextarea::-webkit-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactTextarea:-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactTextarea::-ms-input-placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactTextarea::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #B0B0B0;
  opacity: 1;
  /* Firefox */
}

.contactTextarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #B0B0B0;
}

.contactTextarea::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #B0B0B0;
}

.contactTextarea:focus {
  -webkit-box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
}

.contactButton {
  position: relative;
  width: 200px;
  height: 60px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  text-transform: uppercase;
  font-size: 22px;
  font-weight: 700;
  -webkit-box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 7px 15px rgba(0, 0, 0, 0.1);
  background: #008CFF;
  color: #fff;
  float: right;
}

.contactButton:hover {
  color: #fff;
  background: #008CFF;
}

.contactButton:hover .blockGrad {
  opacity: 0;
}

.contactButton span {
  position: relative;
  z-index: 1;
}

.contactImage {
  max-width: 450px;
  margin: 0 auto;
  min-height: 100%;
  padding: 10px 0 0 0;
}

@media only screen and (max-width: 991px) {
  .contactImage {
    display: none;
  }
}

.domain-options {
  padding-top: 20px;
}

.domain-options .domain-options-inner-main {
  position: relative;
}

.domain-options .domain-options-inner {
  padding: 0;
  margin: 0 80px 0 0;
  list-style: none;
}

.domain-options .domain-options-inner li {
  float: left;
  padding-right: 8px;
}

.domain-options .receiver-domains {
  overflow: hidden;
  padding: 0;
  margin: 0;
  list-style: none;
  position: absolute;
  width: 200px;
  top: 39px;
  right: 0;
  background-color: #fff;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  z-index: 9;
  visibility: hidden;
  opacity: 0;
}

.domain-options .receiver-domains.show {
  visibility: visible;
  opacity: 1;
}

.domain-options .receiver-domains li {
  float: left;
  padding-right: 4px;
  padding-left: 4px;
  width: calc(50% - 4px);
}

.domain-options .receiver-domains li .domain-ckbx {
  width: 100%;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.domain-options .receiver-more-btn {
  position: absolute;
  right: 0;
  top: 0;
}

.domain-options .receiver-more-btn .nav-show-more-btn {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  cursor: pointer;
  width: 80px;
  right: 0;
  top: 0;
  height: 31px;
  border-radius: 4px;
  background-color: #0072CF;
  color: #fff;
  text-align: center;
  line-height: 31px;
  font-size: 16px;
  font-weight: 700;
}

.domain-options .custom-control-input, .domain-options .custom-control-label {
  cursor: pointer;
}

.domain-options .domain-ckbx {
  background: #fff;
  -webkit-box-shadow: 2px 7px 14px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 7px 14px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 5px 16px;
  margin-bottom: 5px;
  float: left;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media only screen and (max-width: 991px) {
  .domain-options .domain-ckbx {
    padding: 3px 10px;
    margin-right: 5px;
    font-size: 13px;
  }
  .domain-options .domain-ckbx svg {
    width: 10px;
  }
}

@media only screen and (max-width: 575px) {
  .domain-options .domain-ckbx {
    padding: 3px 7px;
  }
}

.domain-options .domain-ckbx label.custom-control-label {
  font-size: 16px;
  font-weight: 700;
}

@media only screen and (max-width: 575px) {
  .domain-options .domain-ckbx label.custom-control-label {
    font-size: 14px;
  }
}

.recent-sidebar-searches {
  margin-bottom: 20px;
}

.recent-sidebar-searches a {
  color: #333;
  font-size: 17px;
  font-weight: 500;
  display: block;
  line-height: 31px;
  padding: 7px 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.recent-sidebar-searches a:nth-of-type(2n+1) {
  background-color: rgba(0, 0, 0, 0.05);
}

.recent-sidebar-searches a:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.recent-search-btn {
  position: relative;
  width: 140px;
  height: 45px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  font-size: 17px;
  font-weight: 600;
  background: #008CFF;
  color: #fff;
}

.recent-search-btn:hover {
  background: #008CFF;
  color: #fff;
}

.recent-search-btn:hover .blockGrad {
  opacity: 0;
}

.recent-search-btn span {
  position: relative;
  z-index: 1;
}

.whoisSearchResultsMain {
  position: relative;
  z-index: 1;
}

.whoisSearchResultsMain .whoisSearchResultsBlock {
  min-height: calc(100% - 25px);
  margin-bottom: 25px;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .thumb {
  height: 200px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 4px;
  margin-bottom: 10px;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .domain-name-section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .domain-name-section .domain-name {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  width: 1%;
  font-size: 17px;
  color: #333;
  font-weight: 600;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .domain-name-section .domain-name a {
  color: #333;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn {
  border: none;
  border-radius: 4px;
  margin-left: 16px;
  padding: 0;
  width: 120px;
  height: 37px;
  line-height: 37px;
  font-size: 17px;
  font-weight: 700;
  text-transform: uppercase;
  float: right;
  background: #fff;
  text-align: center;
  color: #fff;
  position: relative;
  overflow: hidden;
}

@media only screen and (max-width: 575px) {
  .whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn {
    width: 80px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
  }
}

.whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn span, .whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn svg {
  position: relative;
  z-index: 1;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn:hover:before, .whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn:focus:before {
  opacity: 0;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn.red-by-btn {
  background: #CF0031;
}

.whoisSearchResultsMain .whoisSearchResultsBlock .right-by-wo-btn.red-by-btn:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #CF0031;
  background: -webkit-gradient(linear, left top, right top, color-stop(1%, #CF0031), to(#B70932));
  background: linear-gradient(to right, #CF0031 1%, #B70932 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#B70932',GradientType=1 );
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}

.whoispageination {
  text-align: center;
  margin: 0;
  padding: 30px 0 0 0;
  list-style: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.whoispageination a {
  background-color: #f7f7f7;
  padding: 0 18px;
  line-height: 40px;
  font-size: 16px;
  font-weight: 600;
  color: #707070;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 4px;
  margin: 0 3px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.whoispageination a:hover, .whoispageination a:focus, .whoispageination a:active, .whoispageination a.active {
  background-color: #0072CF;
  color: #fff;
}

.whoispageination a.disabled {
  pointer-events: none;
  opacity: .65;
}

.alert-danger {
  color: #fff;
  background-color: #CF0031;
  border-color: #CF0031;
}

.alert-success {
  color: #fff;
  background-color: #03BE80;
  border-color: #03BE80;
}

.showmore-btn {
  position: relative;
  width: 170px;
  height: 55px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  text-transform: uppercase;
  font-size: 17px;
  font-weight: 700;
  margin: 0 auto;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  background: #008CFF;
  color: #fff;
}

.showmore-btn:hover {
  color: #fff;
  background: #008CFF;
}

.showmore-btn:hover .blockGrad {
  opacity: 0;
}

.showmore-btn span {
  position: relative;
  z-index: 1;
}

@media only screen and (max-width: 575px) {
  .showmore-btn {
    width: 130px;
    height: 45px;
    font-size: 15px;
  }
}

.footer-ad, .middle-ad, .header-ad {
  padding: 0 15px;
}

.footer-ad img, .middle-ad img, .header-ad img {
  max-width: 100%;
  height: auto;
}

.start-button {
  padding-bottom: 50px;
}

.start-button a {
  z-index: 1;
  color: #fff;
  text-transform: uppercase;
  text-decoration: none;
  display: block;
  margin: 0 auto;
  text-align: center;
  font-size: 40px;
  font-size: 4rem;
  font-weight: 500;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  position: relative;
  left: 0;
  padding: 0;
  width: 180px;
  height: 180px;
  line-height: 182px;
  border-radius: 180px;
  -webkit-transform: translateY(20px) translateX(0);
          transform: translateY(20px) translateX(0);
  -webkit-transform-origin: center center;
          transform-origin: center center;
  border-width: 0;
}

.start-button a .start-ring {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border: 2px #707070 solid;
  opacity: 0;
  -webkit-animation-name: start-ring;
          animation-name: start-ring;
  -webkit-animation-delay: 3.5s;
          animation-delay: 3.5s;
  -webkit-animation-duration: 3.5s;
          animation-duration: 3.5s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
          animation-timing-function: linear;
}

.start-button a .start-background {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  opacity: 0;
  background-color: #0072CF;
}

.start-button a .start-border {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  background-color: #0072CF;
  border: 2px transparent solid;
  background-origin: border-box;
  background-clip: content-box,border-box;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#008CFF), to(#0072CF)), -webkit-gradient(linear, left top, left bottom, from(#707070), to(#707070));
  background-image: linear-gradient(#008CFF, #0072CF), linear-gradient(to bottom, #707070, #707070);
  -webkit-animation-name: start-heartbeat;
          animation-name: start-heartbeat;
  -webkit-animation-delay: 3.5s;
          animation-delay: 3.5s;
  -webkit-animation-duration: 3.5s;
          animation-duration: 3.5s;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}

.start-button a .start-text {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  white-space: nowrap;
  opacity: 1;
  font-weight: 900;
}

@-webkit-keyframes start-heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  8.333% {
    -webkit-transform: scale(0.989);
            transform: scale(0.989);
  }
  16.667% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes start-heartbeat {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  8.333% {
    -webkit-transform: scale(0.989);
            transform: scale(0.989);
  }
  16.667% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@-webkit-keyframes start-ring {
  0% {
    opacity: 0;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  12.5% {
    opacity: 0;
    -webkit-transform: scale(0.995);
            transform: scale(0.995);
  }
  16.667% {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}

@keyframes start-ring {
  0% {
    opacity: 0;
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  12.5% {
    opacity: 0;
    -webkit-transform: scale(0.995);
            transform: scale(0.995);
  }
  16.667% {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale(1.3);
            transform: scale(1.3);
  }
}

.gauge-wrapper {
  visibility: visible;
  position: relative;
  width: 328px;
  margin: 0 auto;
}

.gauge-background {
  height: 328px;
}

#arc-background {
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -164px;
}

.gauge {
  position: absolute;
  top: 0;
  left: 164px;
  margin-left: -164px;
  width: 328px;
  height: 328px;
  z-index: 1;
}

.gauge .gauge-group {
  opacity: 1;
}

.gauge .gauge-ping-progress {
  position: absolute;
  top: 0;
  left: 0;
  opacity: .1;
  -webkit-transform: scale(0.5);
  transform: scale(0.5);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}

.gauge .connecting-message, .gauge .latency-message {
  text-align: center;
  color: #9193a8;
  position: absolute;
  left: 0;
  bottom: 20px;
  font-size: 14px;
  font-size: 1.1999999998rem;
  width: 100%;
  font-weight: 500;
  opacity: 0;
}

.gauge .connecting-message, .gauge .latency-message {
  font-size: 14px;
  font-size: 1.4rem;
}

.gauge .gauge-group-speed {
  position: absolute;
  top: 0;
  left: 0;
  width: 328px;
  height: 328px;
}

.gauge .gauge-speed-needle {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 328px;
  height: 328px;
  -webkit-transform-origin: 164px 164px;
  transform-origin: 164px 164px;
  -webkit-transform: rotateZ(-132deg);
          transform: rotateZ(-132deg);
  -webkit-transform: rotateZ(-31.053deg);
          transform: rotateZ(-31.053deg);
  opacity: 1;
}

.gauge .increments .increment {
  z-index: 1;
  position: absolute;
  width: 20%;
  height: 2em;
  text-align: center;
  opacity: 1;
  font-size: 18px;
  font-weight: 700;
}

.gauge .increments .increment-0 {
  top: 69.2073171%;
  left: 20.1219512%;
  text-align: left;
}

.gauge .increments .increment-1 {
  top: 52.7439024%;
  left: 12.5%;
  text-align: left;
}

.gauge .increments .increment-2 {
  top: 31.7073171%;
  left: 14.6341463%;
  text-align: left;
}

.gauge .increments .increment-3 {
  top: 16.7682927%;
  left: 26.8292683%;
  text-align: left;
}

.gauge .increments .increment-4 {
  top: 9.7560976%;
  left: 0;
  width: 100%;
}

.gauge .increments .increment-5 {
  top: 16.7682927%;
  right: 26.8292683%;
  text-align: right;
}

.gauge .increments .increment-6 {
  top: 31.7073171%;
  right: 14.6341463%;
  text-align: right;
}

.gauge .increments .increment-7 {
  top: 52.7439024%;
  right: 12.5%;
  text-align: right;
}

.gauge .increments .increment-8 {
  top: 69.2073171%;
  right: 20.1219512%;
  text-align: right;
}

.gauge .gauge-unit-icon {
  display: block;
  position: absolute;
  left: 0;
  bottom: 20px;
  font-size: 14px;
  font-size: 1.1999999998rem;
  width: 100%;
  font-weight: 500;
  color: #9193a8;
  line-height: 1.4;
  text-align: center;
  cursor: default;
  font-size: 1.4rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.gauge .gauge-icon {
  position: relative;
  top: 3px;
  font-size: larger;
  margin-right: .2em;
  -webkit-animation: pulse infinite 1s 0s;
          animation: pulse infinite 1s 0s;
  -webkit-animation-direction: alternate;
          animation-direction: alternate;
  -webkit-animation-timing-function: ease-in-out;
          animation-timing-function: ease-in-out;
  width: 18px;
  height: 18px;
}

/**/
.result-container-data {
  max-width: 720px;
  margin: 0 auto 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
      justify-content: space-around;
}

.result-item-container-align-right {
  text-align: right;
  position: relative;
}

.result-item-container-align-center {
  text-align: center;
}

.result-item-container-align-left {
  text-align: left;
}

.result-container-data .result-item-container {
  min-width: 100px;
}

.result-item-container .result-item {
  display: inline-block;
  width: auto;
}

.result-label {
  font-size: 14px;
}

.svg-icon {
  width: 18px;
  height: 18px;
  position: relative;
  top: -1px;
  margin-right: 5px;
}

.result-item-ping .svg-icon path {
  color: #fff38e;
}

.result-item-download .svg-icon path {
  color: #6afff3;
}

.result-item-upload .svg-icon path {
  color: #bf71ff;
}

.result-data-unit {
  color: #9193a8;
  margin-top: -.8em;
  text-transform: none;
}

.result-item-first-class .result-data {
  display: block;
  line-height: 1.2;
  background-color: rgba(26, 27, 46, 0);
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
}

.result-item-first-class .result-data.u-align-left {
  padding-left: 1.7em;
}

.result-data-large {
  font-size: 50px;
  color: #303030;
}

.blog-title {
  font-size: 48px;
  font-weight: 800;
  color: #000;
  text-align: center;
  margin-bottom: 40px;
}

@media only screen and (max-width: 991px) {
  .blog-title {
    font-size: 43px;
    margin-bottom: 40px;
  }
}

@media only screen and (max-width: 767px) {
  .blog-title {
    font-size: 39px;
    margin-bottom: 20px;
  }
}

@media only screen and (max-width: 575px) {
  .blog-title {
    font-size: 30px;
  }
}

.blogColumn-section {
  padding-top: 0;
}

.blogColumn-section .blogColumnsMain {
  background: #fff;
  min-height: calc(100% - 25px);
  margin-bottom: 25px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-align: initial;
      -ms-flex-align: initial;
          align-items: initial;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
          box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 20px;
}

@media only screen and (max-width: 767px) {
  .blogColumn-section .blogColumnsMain {
    padding: 10px;
    -webkit-box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.1);
  }
}

.blogColumn-section .blogColumnsMain .homepageColumnsImage {
  height: 200px;
  margin-bottom: 15px;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 5px;
}

@media only screen and (max-width: 991px) {
  .blogColumn-section .blogColumnsMain .homepageColumnsImage {
    margin-bottom: 10px;
  }
}

.blogColumn-section .blogColumnsMain h2 {
  font-size: 25px;
  font-weight: 700;
  color: #000;
  text-transform: uppercase;
  margin: 0 0 15px 0;
}

@media only screen and (max-width: 991px) {
  .blogColumn-section .blogColumnsMain h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
  }
}

.blogColumn-section .blogColumnsMain h2 a {
  color: #000;
}

.blogColumn-section .blogColumnsMain h2 a:hover, .blogColumn-section .blogColumnsMain h2 a:focus, .blogColumn-section .blogColumnsMain h2 a:active {
  color: #0072CF;
}

.blogColumn-section .blogColumnsMain .date-sec {
  font-size: 14px;
  color: #000;
  margin: 0 0 15px 0;
}

.blogColumn-section .blogColumnsMain .date-sec a {
  background-color: rgba(0, 0, 0, 0.1);
  color: #000;
  border-radius: 100px;
  padding: 1px 10px;
  font-weight: 600;
}

.blogColumn-section .blogColumnsMain p {
  font-size: 18px;
  line-height: 1.3;
  color: #000;
  font-weight: 400;
  margin-bottom: 0;
}

@media only screen and (max-width: 991px) {
  .blogColumn-section .blogColumnsMain p {
    font-size: 16px;
  }
}

@media only screen and (max-width: 767px) {
  .blogColumn-section .blogColumnsMain p {
    font-size: 15px;
    line-height: 25px;
  }
}

.blog-single-image {
  margin-bottom: 20px;
  position: relative;
}

.blog-single-inner {
  max-width: 1260px;
  margin: 0 auto;
}

.homeFaqsRow, .domainDnsSection.homeFaqsRow {
  position: relative;
  z-index: 1;
}

.homeFaqsRow .homefaq-subtitle, .domainDnsSection.homeFaqsRow .homefaq-subtitle {
  font-size: 27px;
  font-weight: 600;
  color: #000;
  margin: 0 0 25px 0;
}

@media only screen and (max-width: 991px) {
  .homeFaqsRow .homefaq-subtitle, .domainDnsSection.homeFaqsRow .homefaq-subtitle {
    font-size: 20px;
    margin: 0 0 13px 0;
  }
}

.homeFaqsRow p, .domainDnsSection.homeFaqsRow p {
  font-size: 18px;
  color: #000;
  font-weight: 300;
  margin: 0 0 32px 0;
}

@media only screen and (max-width: 991px) {
  .homeFaqsRow p, .domainDnsSection.homeFaqsRow p {
    font-size: 16px;
  }
}

.homeFaqsRow p a, .domainDnsSection.homeFaqsRow p a {
  color: #0072CF;
}

.homeFaqsRow ul, .domainDnsSection.homeFaqsRow ul {
  margin-bottom: 30px;
}

.homeFaqsRow ul li, .domainDnsSection.homeFaqsRow ul li {
  font-size: 18px;
  color: #000;
  font-weight: 300;
}

.homeFaqsRow ul li a, .domainDnsSection.homeFaqsRow ul li a {
  color: #0072CF;
}

body.theme-dark {
  background-color: #171717;
}

body.theme-dark .headerBottom .headerBottomBlocks {
  background-color: #303134;
}

body.theme-dark .headerBottom .headerBottomBlocks .headerBottomBlockIcon svg path {
  fill: #fff;
}

body.theme-dark .headerBottom .headerBottomBlocks h2 {
  color: #fff;
}

body.theme-dark .searchSection .inputFiled {
  background-color: #202124;
  color: #fff;
}

body.theme-dark .searchSection .searchInput .searchCross {
  background-color: #171717;
}

body.theme-dark .homepageColumnsArea .homepageColumnsMain {
  background-color: #303134;
}

body.theme-dark .homepageColumnsArea .homepageColumnsMain h2, body.theme-dark .homepageColumnsArea .homepageColumnsMain p {
  color: #fff;
}

body.theme-dark .homepageFaqsAreaMain .homepageFaqsArea {
  background-color: #303134;
}

body.theme-dark .homepageFaqsAreaMain .homepageFaqsArea h1, body.theme-dark .homepageFaqsAreaMain .homepageFaqsArea h4, body.theme-dark .homepageFaqsAreaMain .homepageFaqsArea p {
  color: #fff;
}

body.theme-dark footer .footer-inner {
  background-color: #303134;
}

body.theme-dark .contactDivider {
  background-color: #202124;
}

body.theme-dark .result-content .result-main-title {
  background-color: #303134;
}

body.theme-dark .result-content .result-main-title .left-title-area {
  color: #fff;
}

body.theme-dark .result-content .result-main-title .left-title-area span svg path {
  fill: #fff;
}

body.theme-dark .result-content .result-main-title .left-title-area.enlarge .date-sec {
  color: #fff;
}

body.theme-dark .result-content .result-main-title .left-title-area.enlarge .date-sec a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

body.theme-dark .extension-area ul li {
  background-color: #303134;
}

body.theme-dark .extension-area ul li:nth-of-type(2n+1) {
  background-color: #202124;
}

body.theme-dark .extension-area ul li .left-extension, body.theme-dark .extension-area ul li .left-extension.text-dark {
  color: #fff !important;
}

body.theme-dark .homeFaqsRow .homefaq-subtitle, body.theme-dark .homeFaqsRow p, body.theme-dark .domainDnsSection.homeFaqsRow .homefaq-subtitle, body.theme-dark .domainDnsSection.homeFaqsRow p {
  color: #fff;
}

body.theme-dark .homeFaqsRow ul li, body.theme-dark .domainDnsSection.homeFaqsRow ul li {
  color: #fff;
}

body.theme-dark .domainDnsSection {
  background-color: #303134;
  border-top-color: #757575;
}

body.theme-dark .domainDnsSection h2, body.theme-dark .domainDnsSection h3 {
  color: #fff;
}

body.theme-dark .domainDnsSection .dnsBlock {
  border-color: #757575;
}

body.theme-dark .domainDnsSection .dnsBlock .dnsBlockRow {
  border-color: #757575;
  color: #fff;
}

body.theme-dark .domain-options .domain-ckbx {
  background-color: #303134;
}

body.theme-dark label.custom-control-label {
  color: #fff;
}

body.theme-dark .whoisText {
  background-color: #303134;
  color: #fff;
}

body.theme-dark .recent-sidebar-searches a {
  color: #fff;
}

body.theme-dark .blog-title {
  color: #fff;
}

body.theme-dark .blogColumn-section .blogColumnsMain {
  background-color: #303134;
}

body.theme-dark .blogColumn-section .blogColumnsMain h2 a {
  color: #fff;
}

body.theme-dark .blogColumn-section .blogColumnsMain h2 a:hover, body.theme-dark .blogColumn-section .blogColumnsMain h2 a:focus, body.theme-dark .blogColumn-section .blogColumnsMain h2 a:active {
  color: #0072CF;
}

body.theme-dark .blogColumn-section .blogColumnsMain .date-sec {
  color: #fff;
}

body.theme-dark .blogColumn-section .blogColumnsMain .date-sec a {
  background-color: rgba(255, 255, 255, 0.05);
  color: #fff;
}

body.theme-dark .blogColumn-section .blogColumnsMain p {
  color: #fff;
}

.logos-sec {
  max-width: 100%;
  border-radius: 5px 5px 0 0;
  background-color: #fff;
  -webkit-box-shadow: 2px 4px 15px rgba(0, 0, 0, 0.5);
          box-shadow: 2px 4px 15px rgba(0, 0, 0, 0.5);
  padding: 0 10px;
  position: fixed;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  overflow: auto;
  height: 50px;
  z-index: 9;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: center;
  bottom: 0;
}

.logos-sec a {
  margin: 6px 6px;
  display: block;
  min-width: 110px;
  max-width: 110px;
}

.logos-sec a img {
  max-width: 100%;
  height: auto;
}
/*# sourceMappingURL=style.css.map */