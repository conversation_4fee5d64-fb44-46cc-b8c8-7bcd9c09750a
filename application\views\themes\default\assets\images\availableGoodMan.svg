<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="301.613" height="247.141" viewBox="0 0 301.613 247.141">
  <defs>
    <linearGradient id="linear-gradient" x1="-6.662" y1="0.5" x2="-7.662" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d6969b"/>
      <stop offset="0.992" stop-color="#fab9be"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-9.307" y1="0.165" x2="-10.081" y2="0.724" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fab9be"/>
      <stop offset="0.992" stop-color="#fab9be"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-27.323" y1="0.5" x2="-28.324" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0.008" stop-color="#fab9be"/>
      <stop offset="0.335" stop-color="#f6b5ba"/>
      <stop offset="0.673" stop-color="#e9a9ae"/>
      <stop offset="1" stop-color="#d6969b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-12.148" y1="-0.723" x2="-13.408" y2="0.702" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fab9be"/>
      <stop offset="0.992" stop-color="#d6969b"/>
    </linearGradient>
  </defs>
  <g id="Group_1" data-name="Group 1" transform="translate(0 0)">
    <path id="Path_1891" data-name="Path 1891" d="M17.537,101.914C9.348,105.836-4.275,103.015,1.3,89.76c6-14.266,15.467-28.191,25.862-40.486C39.7,34.449,57.587,14.6,70.205,5.56,94.7-12,93.135,16.6,89.465,28.484,84.159,45.693,65.7,61,51.968,74.544,41.165,85.2,26.469,97.636,17.537,101.914Z" transform="translate(88.54 93.083)" fill="#008cff"/>
    <path id="Path_1892" data-name="Path 1892" d="M81.027,0a59.062,59.062,0,0,1-1.491,14.093C74.978,32.911,57.092,50.948,43.882,66.666c-10.39,12.361-24.622,27-33.42,32.4A16.889,16.889,0,0,1,0,101.466c3.67,2.664,9.943,2.581,14.517.391,8.932-4.273,23.628-16.718,34.431-27.37,13.735-13.546,32.191-28.851,37.5-46.06C89.194,19.506,90.763,1.173,81.027,0Z" transform="translate(91.566 93.135)" fill="#1d43b8"/>
    <path id="Path_1893" data-name="Path 1893" d="M94.259,60.506c-7.72-2.246-38.14-26.253-49.384-33.833C42.842,25.3,34.491,19,30.056,17.932,23.6,16.368,15.968,17,12.745,15.111,9.171,13.016-.633,1.912.032.258.68-1.356,7.618,5.023,11.561,7.693c1.771,1.2,3.547,1.207,5.541,1.145,2.4-.078,4.96-.279,6.709-.424,1.268-.106,2.128-.4,2.033-.9-.156-.816-.168-1.2-1.625-1.888-1.72-.8-6.172-1.609-7.362-2.547-1.29-1.017-1.05-1.4.33-1.229,2.19.279,7.306.737,10.9,1.9A15.929,15.929,0,0,1,33,6.587c3.7,2.826,4.452,5.5,8.792,7.781C59.755,23.8,84.992,32.332,96.56,37.728c5.753,2.681,12.4,9.272,11.361,17.3C107.492,58.423,101.939,62.741,94.259,60.506Z" transform="translate(0 135.469)" fill="url(#linear-gradient)"/>
    <path id="Path_1894" data-name="Path 1894" d="M113.7,162.885H2.091s1.458-31.029,0-57.528C.594,78.182-2.4,54.23,3.554,32.853c1.977-7.1,5.653-17.5,17.925-23.315C30.221,5.394,48.034-.159,63.06,0A87,87,0,0,1,95.965,7.192c17.612,7.658,17.3,15.422,18.455,25.482,2.72,23.617-2.2,57.383-2.2,87.295C112.22,144.435,113.7,162.885,113.7,162.885Z" transform="translate(145.504 84.256)" fill="#008cff"/>
    <path id="Path_1895" data-name="Path 1895" d="M63.75,100.055c0,24.471,1.475,42.921,1.475,42.921H0c16.88-4.016,40.53-8.429,42.167-28.164.776-9.457-4.2-18.858-5.39-28.225-1.212-9.541-.525-17.847,3.363-25.974,3.737-7.831,5.977-16.545,3.737-26.923C42.837,28.936,39.184,23,38.112,18.284c-3.9-17.092,6.681-22.5,20.109-14.959A83.774,83.774,0,0,1,65.387,7.8c.212,1.586.363,3.251.564,4.966C68.666,36.377,63.75,70.143,63.75,100.055Z" transform="translate(193.973 104.165)" fill="#0067bc"/>
    <path id="Path_1896" data-name="Path 1896" d="M67.053,103.393c.838,15.925-17.21,12.467-25.7-.346-6.982-10.54-9.574-20-17.171-37.5C16.474,47.8,9.224,42.464,3.873,27.114-.931,13.328-5.685-9.244,18.8,4.039,46.861,19.26,65.473,73.437,67.053,103.393Z" transform="translate(232.832 93.492)" fill="#1488e6"/>
    <path id="Path_1897" data-name="Path 1897" d="M108.878,47.074c-7.435,0-42.335-16.316-56.735-21.913-2.463-.955-11.088-4.753-16-4.8-7.144-.073-15.992,1.592-19.846.43C11.233,19.263.408,11.549.017,9.421-.748,5.259,24.673,5.974,24.673,5.974S16.512-.656,20.986.053C25.55.779,29.705,1.366,32.9,3.595c3.357,2.341,9.25,7.775,14.333,9.205,20.667,5.815,43.58,6.491,53.925,9.323,6.256,1.715,12.925,6.731,14.97,12C117.346,37.271,121.033,47.074,108.878,47.074Z" transform="translate(181.897 160.357)" fill="url(#linear-gradient-2)"/>
    <path id="Path_1898" data-name="Path 1898" d="M39.737,40.9l-2.273-.475a6.637,6.637,0,0,1-5.379-6.133C31.292,20.589,30.208,0,30.208,0H7.306V36.028a5.327,5.327,0,0,1-4.195,5.189c-.978.218-1.944.441-2.9.681A5.287,5.287,0,0,0,0,43.435C0,49.3,8.58,54.054,19.159,54.054S41.575,49.066,39.737,40.9Z" transform="translate(185.743 45.371)" fill="url(#linear-gradient-3)"/>
    <path id="Path_1899" data-name="Path 1899" d="M24.052,21.572C21.5,28.811,13.886,36.2,5.329,35.777A12.541,12.541,0,0,1,0,34.325V0H22.9C22.9,0,23.46,10.557,24.052,21.572Z" transform="translate(193.047 45.36)" fill="#bf8286"/>
    <path id="Path_1900" data-name="Path 1900" d="M34.53,26.792C30.988,43.84,23.453,48.644,16,48.644c-7.8,0-12.529-3.407-14.886-19.723C-1.431,11.359-.069,4.762,10.053.991c4.3-1.6,15.255-1.754,20.293,3.284C34.787,8.711,35.87,20.346,34.53,26.792Z" transform="translate(182.289 16.445)" fill="#fab9be"/>
    <path id="Path_1901" data-name="Path 1901" d="M51.011,20.012c-.838-6.541-5.01-11.568-10.479-9.8C37.51,5.612,31.271,3.327,27.769,2.2,23.183.724,14.826-.712,9.944.389c-5.206,1.173-6.468,4.675-.6,6.368a21.043,21.043,0,0,0-7.474,5.5c-1.531,1.832-3.983,7.96,1.625,7.8-2.307,6.351,3.955,11.233,6.776,12.708-.6-6.15,2.039-10.429,4.876-11.339,4.223-1.357,14.339-.827,18.813,2.335C40,28.022,40.119,39.249,39.627,42.908c-.168,1.24-4.256,6.8-9.736,6.8-2.508,0-5.038-1.927-8.328-1.927-3.027,0-4.083,1.531-6.429,1.531-3.513,0-3.513-5.826-4.24-6.066C8.615,42.5,5.777,50.113,5.777,60.726c0,9.027,5.569,17.478,13.646,17.478,9.351,0,15.171-3.62,19.438-9.01,5.145-6.507,4.983-15.568,4.011-23.823A18.15,18.15,0,0,0,47.8,39.1C50.1,33.183,51.8,26.2,51.011,20.012Z" transform="translate(172.215 0)" fill="#1e2859"/>
    <path id="Path_1902" data-name="Path 1902" d="M11.9.572c0,.475-2.664,1.665-5.949,1.665S0,.885,0,.232C0-.2,2.664.109,5.949.109S11.9.019,11.9.572Z" transform="translate(189.159 53.647)" fill="#fff"/>
    <path id="Path_1903" data-name="Path 1903" d="M6.721,6.463c-.5,3.536-3.452,7.01-5.251,6.463C.007,12.484-.06,10.032.024,6.463.1,3.095.571,0,2.761,0S7.324,2.212,6.721,6.463Z" transform="translate(214.947 34.244)" fill="#d6969b"/>
    <path id="Path_1904" data-name="Path 1904" d="M85.975,28.109c-3.536,1.642-7.329,1-10.485.251-7.524-1.776-14.6-5.5-21.617-8.647-9.6-4.307-19.2-8.686-28.934-12.68C20.689,5.292,14.377,2.795,9.813,2.08,6.691,1.594-.632,2.733.044,1.89,1.636-.093,10.7-.585,16.12.745c7.8,1.916,13.182,3.463,24.639,9.295,17.59,8.948,34.76,16.053,47.211,12.931C91.684,22.038,89.349,26.54,85.975,28.109Z" transform="translate(209.57 178.92)" fill="url(#linear-gradient-4)"/>
    <path id="Path_1905" data-name="Path 1905" d="M21.438,0,0,2.419,9.92,18.936l21.438-2.424Z" transform="translate(175.161 149.814)" fill="#1e2859"/>
    <path id="Path_1906" data-name="Path 1906" d="M37.8,32.309c-1.592.86,0-5.608-3.391-11.44S25.736,12.9,20.8,11.022C16.2,9.262,9.755,8.81,4.259,7.581-8.5,4.738,10.75-.546,18.514.046,29.456.884,37.126,2.358,39.069,14.664,39.522,17.546,40.136,31.041,37.8,32.309Z" transform="translate(262.117 172.396)" fill="#fff"/>
    <path id="Path_1907" data-name="Path 1907" d="M2.744,36.611c-.508-1.38,5.2-2.62,6.172-6.8C10.107,24.719,5.66,17.7,1.65,14.028c-2.525-2.313-1.7-5.508-.508-7.814C2.862,2.879,4.85-1.774,9.257.695c5.625,3.15,9.8,11.144,11.149,15.344,1.223,3.787,2.022,11.032,1.005,13.741C19.262,35.516,3.839,39.583,2.744,36.611Z" transform="translate(94.428 160.61)" fill="#fff"/>
    <path id="Path_1908" data-name="Path 1908" d="M1.871,2.882a.275.275,0,0,0-.028-.112.282.282,0,0,0-.05-.1c-.011-.011-.006-.022-.017-.034A1.719,1.719,0,0,1,1.362.5c.006-.011,0-.022.011-.028A.236.236,0,0,0,1.4.357.212.212,0,0,0,1.413.24c0-.011.006-.017.006-.028C1.407.179,1.379.168,1.362.14A.4.4,0,0,0,1.318.056C1.306.05,1.3.056,1.29.045A.231.231,0,0,0,1.184.017.232.232,0,0,0,1.061.006C1.049.006,1.044,0,1.033,0A1.531,1.531,0,0,0,0,1.62a1.683,1.683,0,0,0,1.53,1.558c.017,0,.034.006.05.006a.307.307,0,0,0,.218-.1c.006-.006.017-.006.022-.011s.011-.045.022-.067a.284.284,0,0,0,.05-.089C1.882,2.9,1.871,2.893,1.871,2.882Z" transform="translate(184.636 37.205)" fill="#1e2859"/>
    <path id="Path_1909" data-name="Path 1909" d="M1.871,2.882a.275.275,0,0,0-.028-.112.284.284,0,0,0-.05-.1c-.011-.011-.006-.022-.017-.034A1.719,1.719,0,0,1,1.362.5c.006-.011,0-.022.011-.028A.236.236,0,0,0,1.4.357.213.213,0,0,0,1.413.24c0-.011.006-.017.006-.028C1.407.179,1.379.168,1.362.14A.4.4,0,0,0,1.318.056C1.306.05,1.3.056,1.29.045A.231.231,0,0,0,1.183.017.232.232,0,0,0,1.061.006C1.049.006,1.044,0,1.033,0A1.531,1.531,0,0,0,0,1.62a1.683,1.683,0,0,0,1.53,1.558c.017,0,.034.006.05.006a.307.307,0,0,0,.218-.1c.006-.006.017-.006.022-.011s.011-.045.022-.067a.283.283,0,0,0,.05-.089C1.876,2.9,1.865,2.9,1.871,2.882Z" transform="translate(200.267 37.4)" fill="#1e2859"/>
    <path id="Path_1910" data-name="Path 1910" d="M6.024,22.053l8.881-7.658A29.635,29.635,0,0,1,9.08,11.384C6.818,9.507,5.013,7.742,5.739,3.357,6.03,1.609,9.488-.006,6.516,0A6.223,6.223,0,0,0,1.26,3.061,13.97,13.97,0,0,0,.081,10.529C.539,14.841,3.092,18.846,6.024,22.053Z" transform="translate(183.966 83.137)" fill="#fff"/>
    <path id="Path_1911" data-name="Path 1911" d="M24.522,1.389c-1.011-2.737-2.609-.67-2.743-.168-1.056,4.055-2.938,6.524-7.418,8.921A28.15,28.15,0,0,1,1.519,13.37,11.8,11.8,0,0,0,0,13.387l7.317,9.267C15.646,19.548,26.968,10.5,24.522,1.389Z" transform="translate(201.721 84.429)" fill="#fff"/>
    <path id="Path_1912" data-name="Path 1912" d="M13.326,2.2A12.4,12.4,0,0,0,.166,1.614,1.761,1.761,0,0,0,.043,2.731a.308.308,0,0,0,.43.095A12.093,12.093,0,0,1,13,2.737a.285.285,0,0,0,.162.045c.017,0,.033-.011.05-.011s.028-.006.045-.011a.3.3,0,0,0,.173-.128A.335.335,0,0,0,13.326,2.2Z" transform="translate(195.739 33.278)" fill="#1e2859"/>
    <path id="Path_1913" data-name="Path 1913" d="M11.642,13.54c-1.346-.128-2.893-.5-3.3-1.033a.33.33,0,0,1-.078-.274c.145-.888.318-1.771.508-2.7.324-1.62.687-3.458.966-5.725.156-1.268-.207-2.7-1.609-3.095C8.151.548,8.017.168,7.85.151,3.716-.352,1.275.486.1,1.4c-.022.017-.022.045-.033.061a.257.257,0,0,0-.056.1.251.251,0,0,0-.006.112c0,.028-.011.05-.006.073s.028.022.034.039,0,.034.011.05.039.011.056.028a.3.3,0,0,0,.134.067c.022.006.033.028.056.028s.039-.017.061-.022.028.006.045,0A15.381,15.381,0,0,1,7.76,1.251c1.5.263,1.413,1.961,1.352,2.474C8.838,5.971,8.475,7.792,8.157,9.4c-.184.933-.363,1.827-.508,2.72a.967.967,0,0,0,.2.754c.7.916,3.039,1.207,3.737,1.274h.028a.3.3,0,0,0,.028-.609Z" transform="translate(181.429 34.25)" fill="#1e2859"/>
  </g>
</svg>
