/* https://github.com/demonicious/vjax */

const vjax={request:function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("method")||(r.method="GET"),r.method=r.method.toUpperCase(),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data"))if("POST"==r.method){let s;for(s in e=new FormData,r.data)e.append(s,r.data[s])}else{let s;for(s in e="",r.data)e+=`${s}=${r.data[s]}`;let t=r.url.slice(-1);"/"!=t&&"\\"!=t||(r.url=r.url.substring(0,r.url.length-1)),r.url+=`?${e}`}return this.xhr.open(r.method,r.url,!0),this.xhr.send(e),r.hasOwnProperty("onSend")&&r.onSend(),this},post:function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data")){let s;for(s in e=new FormData,r.data)e.append(s,r.data[s])}return this.xhr.open("POST",r.url,!0),this.xhr.send(e),r.hasOwnProperty("onSend")&&r.onSend(),this},get:function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data")){let s;for(s in e="",r.data)e+=`${s}=${r.data[s]}`;let t=r.url.slice(-1);"/"!=t&&"\\"!=t||(r.url=r.url.substring(0,r.url.length-1)),r.url+=`?${e}`}return this.xhr.open("GET",r.url,!0),this.xhr.send(),r.hasOwnProperty("onSend")&&r.onSend(),this}},vjaxClass=function(){this.request=function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("method")||(r.method="GET"),r.method=r.method.toUpperCase(),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data"))if("POST"==r.method){let s;for(s in e=new FormData,r.data)e.append(s,r.data[s])}else{let s;for(s in e="",r.data)e+=`${s}=${r.data[s]}`;let t=r.url.slice(-1);"/"!=t&&"\\"!=t||(r.url=r.url.substring(0,r.url.length-1)),r.url+=`?${e}`}return this.xhr.open(r.method,r.url,!0),this.xhr.send(e),r.hasOwnProperty("onSend")&&r.onSend(),this},this.post=function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data")){let s;for(s in e=new FormData,r.data)e.append(s,r.data[s])}return this.xhr.open("POST",r.url,!0),this.xhr.send(e),r.hasOwnProperty("onSend")&&r.onSend(),this},this.get=function(r){this.xhr=null,this.xhr=new XMLHttpRequest,r.onProgress&&(this.xhr.onprogress=()=>{r.onProgress()}),r.hasOwnProperty("onResponse")&&(this.xhr.onload=()=>{let e={url:this.xhr.responseURL,status:this.xhr.status,headers:this.xhr.getAllResponseHeaders(),text:this.xhr.responseText};this.xhr.getResponseHeader("content-type").includes("application/json")&&(e.json=JSON.parse(e.text)),r.onResponse(e)}),r.hasOwnProperty("onError")&&(this.xhr.onerror=e=>{r.onError(e)}),r.hasOwnProperty("onAbort")&&(this.xhr.onabort=()=>{r.onAbort()}),r.hasOwnProperty("preProcess")&&r.preProcess();let e=null;if(r.hasOwnProperty("data")){let s;for(s in e="",r.data)e+=`${s}=${r.data[s]}`;let t=r.url.slice(-1);"/"!=t&&"\\"!=t||(r.url=r.url.substring(0,r.url.length-1)),r.url+=`?${e}`}return this.xhr.open("GET",r.url,!0),this.xhr.send(),r.hasOwnProperty("onSend")&&r.onSend(),this}};