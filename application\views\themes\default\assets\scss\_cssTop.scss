*, body {
    font-family: $fontFamily;
}
body{
    overflow-x:hidden;
    background:$colorBody;
    font-size:14px;
}
html, body{
    height:100%;
}
body, p, form, input, h1, h2, h3, h4, h5, h6, p, form, ul, li, ol, article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    font-family: $fontFamily;
    color:$colorPrimary;
    @include transition();
}
a{
    color:$colorPrimary;
    text-decoration:none;
    font-family: $fontFamily;
    @include transition();
}
a:hover, a:focus, a:active {
    text-decoration:none; color:$colorBlack;
}
button, a, input, textarea{
    outline:0 !important;
}

/* Margin */
.m-all-0{ margin:0px !important;} .m-all-10{ margin:10px !important;} .m-all-20{ margin:20px !important;} .m-all-30{ margin:30px !important;} .m-all-40{ margin:40px !important;}

.m-l-0{ margin-left:0px !important;} .m-r-0{ margin-right:0px !important;} .m-t-0{ margin-top:0px !important;} .m-b-0{ margin-bottom:0px !important;}

.m-l-1{ margin-left:1px !important;} .m-l-2{ margin-left:2px !important;} .m-l-3{ margin-left:3px !important;} .m-l-4{ margin-left:4px !important;} .m-l-5{ margin-left:5px !important;} .m-l-6{ margin-left:6px !important;} .m-l-7{ margin-left:7px !important;} .m-l-8{ margin-left:8px !important;} .m-l-9{ margin-left:9px !important;}

.m-r-1{ margin-right:1px !important;} .m-r-2{ margin-right:2px !important;} .m-r-3{ margin-right:3px !important;} .m-r-4{ margin-right:4px !important;} .m-r-5{ margin-right:5px !important;} .m-r-6{ margin-right:6px !important;} .m-r-7{ margin-right:7px !important;} .m-r-8{ margin-right:8px !important;} .m-r-9{ margin-right:9px !important;}

.m-t-1{ margin-top:1px !important;} .m-t-2{ margin-top:2px !important;} .m-t-3{ margin-top:3px !important;} .m-t-4{ margin-top:4px !important;} .m-t-5{ margin-top:5px !important;} .m-t-6{ margin-top:6px !important;} .m-t-7{ margin-top:7px !important;} .m-t-8{ margin-top:8px !important;} .m-t-9{ margin-top:9px !important;}

.m-b-1{ margin-bottom:1px !important;} .m-b-2{ margin-bottom:2px !important;} .m-b-3{ margin-bottom:3px !important;} .m-b-4{ margin-bottom:4px !important;} .m-b-5{ margin-bottom:5px !important;} .m-b-6{ margin-bottom:6px !important;} .m-b-7{ margin-bottom:7px !important;} .m-b-8{ margin-bottom:8px !important;} .m-b-9{ margin-bottom:9px !important;}

.m-l-10{ margin-left:10px !important;} .m-r-10{ margin-right:10px !important;} .m-t-10{ margin-top:10px !important;} .m-b-10{ margin-bottom:10px !important;}

.m-l-15{ margin-left:15px !important;} .m-r-15{ margin-right:15px !important;} .m-t-15{ margin-top:15px !important;} .m-b-15{ margin-bottom:15px !important;}

.m-l-20{ margin-left:20px !important;} .m-r-20{ margin-right:20px !important;} .m-t-20{ margin-top:20px !important;} .m-b-20{ margin-bottom:20px !important;}

.m-l-25{ margin-left:25px !important;} .m-r-25{ margin-right:25px !important;} .m-t-25{ margin-top:25px !important;} .m-b-25{ margin-bottom:25px !important;}

.m-l-30{ margin-left:30px !important;} .m-r-30{ margin-right:30px !important;} .m-t-30{ margin-top:30px !important;} .m-b-30{ margin-bottom:30px !important;}

.m-l-35{ margin-left:35px !important;} .m-r-35{ margin-right:35px !important;} .m-t-35{ margin-top:35px !important;} .m-b-35{ margin-bottom:35px !important;}

.m-l-40{ margin-left:40px !important;} .m-r-40{ margin-right:40px !important;} .m-t-40{ margin-top:40px !important;} .m-b-40{ margin-bottom:40px !important;}

.m-l-45{ margin-left:45px !important;} .m-r-45{ margin-right:45px !important;} .m-t-45{ margin-top:45px !important;} .m-b-45{ margin-bottom:45px !important;}

.m-l-50{ margin-left:40px !important;} .m-r-50{ margin-right:40px !important;} .m-t-50{ margin-top:40px !important;} .m-b-50{ margin-bottom:40px !important;}

/* Padding */
.p-all-0{ padding:0px !important;} .p-all-10{ padding:10px !important;} .p-all-20{ padding:20px !important;} .p-all-30{ padding:30px !important;} .p-all-40{ padding:40px !important;}

.p-l-0{ padding-left:0px !important;} .p-r-0{ padding-right:0px !important;} .p-t-0{ padding-top:0px !important;} .p-b-0{ padding-bottom:0px !important;}

.p-l-1{ padding-left:1px !important;} .p-l-2{ padding-left:2px !important;} .p-l-3{ padding-left:3px !important;} .p-l-4{ padding-left:4px !important;} .p-l-5{ padding-left:5px !important;} .p-l-6{ padding-left:6px !important;} .p-l-7{ padding-left:7px !important;} .p-l-8{ padding-left:8px !important;} .p-l-9{ padding-left:9px !important;}

.p-r-1{ padding-right:1px !important;} .p-r-2{ padding-right:2px !important;} .p-r-3{ padding-right:3px !important;} .p-r-4{ padding-right:4px !important;} .p-r-5{ padding-right:5px !important;} .p-r-6{ padding-right:6px !important;} .p-r-7{ padding-right:7px !important;} .p-r-8{ padding-right:8px !important;} .p-r-9{ padding-right:9px !important;}

.p-t-1{ padding-top:1px !important;} .p-t-2{ padding-top:2px !important;} .p-t-3{ padding-top:3px !important;} .p-t-4{ padding-top:4px !important;} .p-t-5{ padding-top:5px !important;} .p-t-6{ padding-top:6px !important;} .p-t-7{ padding-top:7px !important;} .p-t-8{ padding-top:8px !important;} .p-t-9{ padding-top:9px !important;}

.p-b-1{ padding-bottom:1px !important;} .p-b-2{ padding-bottom:2px !important;} .p-b-3{ padding-bottom:3px !important;} .p-b-4{ padding-bottom:4px !important;} .p-b-5{ padding-bottom:5px !important;} .p-b-6{ padding-bottom:6px !important;} .p-b-7{ padding-bottom:7px !important;} .p-b-8{ padding-bottom:8px !important;} .p-b-9{ padding-bottom:9px !important;}

.p-l-10{ padding-left:10px !important;} .p-r-10{ padding-right:10px !important;} .p-t-10{ padding-top:10px !important;} .p-b-10{ padding-bottom:10px !important;}

.p-l-15{ padding-left:15px !important;} .p-r-15{ padding-right:15px !important;} .p-t-15{ padding-top:15px !important;} .p-b-15{ padding-bottom:15px !important;}

.p-l-20{ padding-left:20px !important;} .p-r-20{ padding-right:20px !important;} .p-t-20{ padding-top:20px !important;} .p-b-20{ padding-bottom:20px !important;}

.p-l-25{ padding-left:25px !important;} .p-r-25{ padding-right:25px !important;} .p-t-25{ padding-top:25px !important;} .p-b-25{ padding-bottom:25px !important;}

.p-l-30{ padding-left:30px !important;} .p-r-30{ padding-right:30px !important;} .p-t-30{ padding-top:30px !important;} .p-b-30{ padding-bottom:30px !important;}

.p-l-35{ padding-left:35px !important;} .p-r-35{ padding-right:35px !important;} .p-t-35{ padding-top:35px !important;} .p-b-35{ padding-bottom:35px !important;}

.p-l-40{ padding-left:40px !important;} .p-r-40{ padding-right:40px !important;} .p-t-40{ padding-top:40px !important;} .p-b-40{ padding-bottom:40px !important;}

.p-l-45{ padding-left:45px !important;} .p-r-45{ padding-right:45px !important;} .p-t-45{ padding-top:45px !important;} .p-b-45{ padding-bottom:45px !important;}

.p-l-50{ padding-left:50px !important;} .p-r-50{ padding-right:50px !important;} .p-t-50{ padding-top:50px !important;} .p-b-50{ padding-bottom:50px !important;}

.border-left-none{ border-left:none !important;} .border-right-none{ border-right:none !important;} .border-top-none{ border-top:none !important;} .border-bottom-none{ border-bottom:none !important;} .border-all-none{ border:none !important;}

.cursor-pointer{
	cursor:pointer;
}
.form-control, .btn.focus, .btn:focus, .custom-select:focus, .form-control:focus{
    box-shadow:none;
    // border-color:lighten($colorLightGrey, 60%);
}
label{
    color: $colorLightGrey;
    font-weight: 600;
    font-size: 18px;
}
label.custom-control-label{
    color: $colorLightGrey;
    font-weight: 400;
    font-size: 15px;
}
.custom-control-input:focus ~ .custom-control-label:before{
    box-shadow:none;
}
.customInput{
    height: 50px;
    font-weight: 300;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 18px;
    @include inputPlaceholder{
        color: lighten($colorSecondaryLight, 20%);
        opacity: 1;
    }
}
.customTextarea{
    font-weight: 300;
    padding: 12px 20px;
    font-size: 18px;
    @include inputPlaceholder{
        color: lighten($colorSecondaryLight, 20%);
        opacity: 1;
    }
}
.form-control:focus {
    border-color:lighten($colorPrimary, 30%);
}
.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-toggle:focus{
    box-shadow: none;
}
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
    box-shadow: none;
}
.positionRelative{
	position:relative;
}
.img-responsivee{
	@include imageResponsive();
}