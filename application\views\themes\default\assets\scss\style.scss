@import "variables";
@import "mixin";
@import "cssTop";
.container{
    position: relative;
}
.mainSection{
    min-height: 100%;
    position: relative;
    overflow: hidden;
    &::before{
        content: '';
        position: absolute;
        z-index: -1;
        left: -170px;
        top: 260px;
        width: 640px;
        height: 639px;
        background: url(../images/leftbg.svg) center center no-repeat;
        background-size: 100% 100%;
    }
    &::after{
        content: '';
        position: absolute;
        z-index: -1;
        right: -170px;
        bottom: 80px;
        width: 597px;
        height: 536px;
        background: url(../images/leftbg.svg) center center no-repeat;
        background-size: 100% 100%;
    }
}
.mainPadding{
    padding-left: 140px !important;
    padding-right: 140px !important;
    @include mediaQuery("desktop", max) {
        padding-left: 50px !important;
        padding-right: 50px !important;
    }
    @include mediaQuery("tablet", max) {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }
}
.toggle-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.toggle-slot {
    position: relative;
    width: 56px;
    height: 30px;
    border: 1px solid white;
    border-radius: 100px;
    background-color: white;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
    transition: background-color 250ms;
}

.toggle-checkbox:checked ~ .toggle-slot {
    background-color: #374151;
    border-color: #374151;
}

.toggle-button {
    transform: translate(29px, 3px);
    position: absolute;
    height: 22px;
    width: 22px;
    border-radius: 50%;
    background-color: #ffdaa3;
    box-shadow: inset 0px 0px 0px 3px #fdb23e;
    transition: background-color 250ms, border-color 250ms, transform 500ms cubic-bezier(.26,2,.46,.71);
}

.toggle-checkbox:checked ~ .toggle-slot .toggle-button {
    background-color: #485367;
    box-shadow: inset 0px 0px 0px 3px white;
    transform: translate(3px, 3px);
}

.sun-icon-wrapper {
    position: absolute;
    height: 20px;
    width: 20px;
    opacity: 1;
    transform: translate(6px, -2px) rotate(15deg);
    transform-origin: 50% 50%;
    transition: opacity 150ms, transform 500ms cubic-bezier(.26,2,.46,.71);
}

.toggle-checkbox:checked ~ .toggle-slot .sun-icon-wrapper {
    opacity: 0;
    transform: translate(4px, -2px) rotate(0deg);
}

.moon-icon-wrapper {
    position: absolute;
    height: 18px;
    width: 18px;
    opacity: 0;
    transform: translate(28px, -2px) rotate(0deg);
    transform-origin: 50% 50%;
    transition: opacity 150ms, transform 500ms cubic-bezier(.26,2.5,.46,.71);
}

.toggle-checkbox:checked ~ .toggle-slot .moon-icon-wrapper {
    opacity: 1;
    transform: translate(30px, -2px) rotate(-15deg);
}
header{
    position: relative;
    z-index: 2;
    background: url(../images/headerGradient.svg) center top no-repeat;
    height: 153px;
    padding: 43px 0 0 0;
    margin-bottom: 66px;
    @include mediaQuery("laptop", min) {
        background-size: cover;
        background-position: center bottom;
    }
    @include mediaQuery("tabletWide", max) {
        height: 74px;
        padding: 15px 0 0 0;
        margin-bottom: 25px;
    }
    nav{
        .navbarMob{
            @include mediaQuery("tabletWide", max) {
                border-radius: 4px;
                background: $colorWhite;
                box-shadow: 0 10px 10px transparentize($colorBlack, .9);
                margin-top: 7px;
                position: absolute;
                top: 45px;
                width: 100%;
            }
        }
    }
    .navbar-brand{
        max-width: 190px;
    }
    .navbar-dark{
        .headerNavigation{
            margin-left: auto;
            @include mediaQuery("tabletWide", max) {
                padding: 12px 0;
            }
            li{
                a.nav-link{
                    font-size: 21px;
                    color: $colorWhite;
                    padding: 0 16px;
                    @include mediaQuery("tabletWide", max) {
                        color: $colorBlack;
                        font-size: 16px;
                        line-height: 32px;
                    }
                }
                &:hover, &.active{
                    a.nav-link{
                        @include mediaQuery("tabletWide", max) {
                            color: $colorPrimary;
                        }
                    }
                }
            }
        }
    }
}
.blockGrad{
    @include transition();
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 0;
    background: $gradBlueLight;
    background: -moz-linear-gradient(left,  $gradBlueLight 0%, $gradBlueDark 100%);
    background: -webkit-linear-gradient(left,  $gradBlueLight 0%,$gradBlueDark 100%);
    background: linear-gradient(to right,  $gradBlueLight 0%,$gradBlueDark 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$gradBlueLight', endColorstr='$gradBlueDark',GradientType=1 );
}
.headerBottom{
    position: relative;
    z-index: 1;
    padding: 0 0 44px 0;
    @include flexCenter(row, center, initial);
    @include mediaQuery("desktop", max) {
        flex-wrap: wrap;
    }
    @include mediaQuery("tabletWide", max) {
        padding-bottom: 30px;
    }
    @include mediaQuery("tablet", max) {
        padding-bottom: 22px;
    }
    .headerBottomBlocks{
        position: relative;
        overflow: hidden;
        background: $colorWhite;
        margin-right: 35px;
        border-radius: 4px;
        padding: 15px 12px 20px;
        @include transition();
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        @include flexCenter(column);
        flex: 1;
        @include mediaQuery("desktop", max) {
            flex: auto;
            margin: 0 8px 15px 8px;
        }
        @include mediaQuery("tabletWide", max) {
            padding: 15px 12px 15px;
            margin-right: 15px;
        }
        @include mediaQuery("tablet", max) {
            margin-right: 5px;
            padding: 10px 0 10px;
            box-shadow: 0px 9px 10px transparentize($colorBlack, .9);
        }
        &:last-child{
            margin-right: 0;
        }
        .blockGrad{
            opacity: 0;
        }
        .headerBottomBlockIcon{
            position: relative;
            z-index: 1;
            width: 54px;
            height: 54px;
            margin-bottom: 10px;
            @include flexCenter();
            @include mediaQuery("tabletWide", max) {
                width: 34px;
                height: 34px;
                margin-bottom: 7px;
            }
            @include mediaQuery("tablet", max) {
                width: 34px;
                height: 34px;
                margin-bottom: 5px;
            }
            svg{
                max-width: 100%;
                max-height: 100%;
                path{
                    @include transition();
                    fill: $colorSecondary;
                }
                circle{
                    @include transition();
                    fill: $colorSecondary;
                }
            }
        }
        .headerBottomBlocks-title{
            position: relative;
            z-index: 1;
            font-size: 18px;
            color: $colorSecondary;
            text-transform: uppercase;
            @include transition();
            text-align: center;
            line-height: 1;
            margin: 0;
            font-weight: 700;
            @include mediaQuery("tabletWide", max) {
                font-size: 14px;
            }
            @include mediaQuery("tablet", max) {
                font-size: 9px;
            }
        }
        &:hover, &.active{
            .blockGrad{
                opacity: 1;
            }
            .headerBottomBlockIcon{
                svg{
                    path{
                        fill: $colorWhite;
                    }
                    circle{
                        fill: $colorWhite;
                    }
                }
            }
            .headerBottomBlocks-title{
                color: $colorWhite;
            }
        }
    }
}
.searchSection{
    @include flexCenter();
    .searchInput{
        flex: 1;
        position: relative;
        .searchCross{
            cursor: pointer;
            width: 38px;
            height: 38px;
            border-radius: 100px;
            background: darken($colorWhite, 7%);
            @include flexCenter();
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            @include mediaQuery("tablet", max) {
                right: 10px;
                width: 33px;
                height: 33px;
            }
            svg{
                @include mediaQuery("tablet", max) {
                    width: 25px;
                    height: 25px;
                }
            }
        }
    }
    .inputFiled{
        width: 100%;
        background: $colorWhite;
        border-radius: 4px;
        padding: 15px 37px;
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        height: 76px;
        border: none;
        font-size: 24px;
        color: $colorSecondary;
        &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
            color: $colorPlaceholder;
            opacity: 1; /* Firefox */
        }

        &:-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: $colorPlaceholder;
        }

        &::-ms-input-placeholder { /* Microsoft Edge */
            color: $colorPlaceholder;
        }
        @include mediaQuery("tabletWide", max) {
            height: 60px;
            padding: 10px 22px;
            font-size: 20px;
        }
        @include mediaQuery("tablet", max) {
            height: 50px;
        }
        @include mediaQuery("phablet", max) {
            font-size: 18px;
        }
    }
    .searchButton{
        position: relative;
        width: 260px;
        height: 76px;
        border-radius: 4px;
        @include flexCenter();
        text-transform: uppercase;
        font-size: 24px;
        font-weight: 700;
        margin-left: 16px;
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        background: $gradBlueLight;
        color: $colorWhite;
        &:hover{
            background: $gradBlueLight;
            .blockGrad{
                opacity: 0;
            }
        }
        span{
            position: relative;
            z-index: 1;
        }
        @include mediaQuery("tabletWide", max) {
            width: 190px;
            height: 60px;
            font-size: 20px;
        }
        @include mediaQuery("tablet", max) {
            height: 50px;
        }
        @include mediaQuery("phablet", max) {
            width: 80px;
            font-size: 14px;
        }
    }
}
.resultAvailorNotMain{
    padding: 64px 0 0 0;
    @include mediaQuery("tabletWide", max) {
        padding-top: 25px;
    }
    .resultAvailorNot{
        position: relative;
        border-radius: 4px;
        z-index: 1;
        &.availabel{
            background:  $colorGreen;
            background: -moz-linear-gradient(left,   $colorGreen 1%, #009ED9 100%);
            background: -webkit-linear-gradient(left,   $colorGreen 1%,#009ED9 100%);
            background: linear-gradient(to right,   $colorGreen 1%,#009ED9 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' $colorGreen', endColorstr='#009ED9',GradientType=1 );
            .availMan{
                position: absolute;
                right: 43px;
                bottom: 0;
                width: 301px;
                height: 247px;
                background: url(../images/availableGoodMan.svg) center center no-repeat;
                @include mediaQuery("tabletWide", max) {
                    display: none;
                }
            }
            .leftSec{
                &:before{
                    content: '';
                    left: -21px;
                    top: 50%;
                    transform: translateY(-50%);
                    position: absolute;
                    height: 162px;
                    width: 162px;
                    background: url(../images/availableGood.svg) center center no-repeat;
                    background-size: 100%;
                    @include mediaQuery("tabletWide", max) {
                        width: 112px;
                        height: 112px;
                    }
                }
            }
        }
        &.notAvailabel{
            background:  #CF0031;
            background: -moz-linear-gradient(left,   #CF0031 1%, #3768C9 100%);
            background: -webkit-linear-gradient(left,   #CF0031 1%,#3768C9 100%);
            background: linear-gradient(to right,   #CF0031 1%,#3768C9 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#3768C9',GradientType=1 );
            .availMan{
                position: absolute;
                right: 43px;
                bottom: -59px;
                width: 245px;
                height: 306px;
                background: url(../images/availableNotMan.svg) center center no-repeat;
                @include mediaQuery("tabletWide", max) {
                    display: none;
                }
            }
            .leftSec{
                &:before{
                    content: '';
                    left: -21px;
                    top: 50%;
                    transform: translateY(-50%);
                    position: absolute;
                    height: 162px;
                    width: 162px;
                    background: url(../images/availableNot.svg) center center no-repeat;
                    background-size: 100%;
                    @include mediaQuery("tabletWide", max) {
                        width: 112px;
                        height: 112px;
                    }
                }
            }
        }
        .leftSec{
            position: relative;
            overflow: hidden;
            padding: 65px 0 65px 50px;
            @include mediaQuery("tabletWide", max) {
                padding: 25px 0 27px 30px;
            }
            @include mediaQuery("phablet", max) {
                padding: 17px 0 17px 22px;
            }
            h1{
                position: relative;
                z-index: 1;
            }
            h2{
                font-size: 24px;
                line-height: 29px;
                font-weight: 400;
                color: $colorWhite;
                position: relative;
                z-index: 1;
                letter-spacing: -1px;
                @include mediaQuery("phablet", max) {
                    font-size: 20px;
                    margin-bottom: 2px;
                }
            }
            h3{
                font-size: 38px;
                font-weight: 800;
                line-height: 46px;
                color: $colorWhite;
                margin: 0;
                position: relative;
                z-index: 1;
                letter-spacing: -1px;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex-wrap: wrap;
                @include mediaQuery("phablet", max) {
                    font-size: 33px;
                    line-height: 35px;
                }
                .domainname-text{
                    word-wrap: anywhere;
                }
                .whois-btn{
                    font-weight: 500;
                    font-size: 18px;
                    border-radius: 4px;
                    border: $colorWhite 1px solid;
                    padding: 8px 15px;
                    line-height: 20px;
                    color: $colorWhite;
                    margin-left: 20px;
                    position: relative;
                    bottom: -3px;
                    letter-spacing: .5px;
                    @include transition();
                    &:hover,
                    &:focus,
                    &:active{
                        border: #f55e82 1px solid;
                        background: #91113d;
                    }
                }
                .buy-price{
                    font-weight: 400;
                    font-size: 30px;
                    margin-left: 10px;
                    letter-spacing: -2px;
                    position: relative;
                    bottom: -3px;
                }
                .buy-now{
                    font-weight: 700;
                    font-size: 18px;
                    border-radius: 4px;
                    padding: 10px 17px;
                    line-height: 20px;
                    color: $colorWhite;
                    margin-left: 20px;
                    position: relative;
                    bottom: -3px;
                    letter-spacing: .5px;
                    background: #03BE80;
                    @include transition();
                    width: 120px;
                    text-transform: uppercase;

                    text-align: center;
                    position: relative;
                    overflow: hidden;

                    &::before{
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        background: #03BE80;
                        background: -webkit-gradient(linear, left top, right top, color-stop(1%, #03BE80), to(#00954F));
                        background: linear-gradient(to right, #03BE80 1%, #00954F 100%);
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #03BE80', endColorstr='#00954F',GradientType=1 );
                        -webkit-transition: all 0.2s ease-in-out;
                        transition: all 0.2s ease-in-out;
                    }
                    span{
                        position: relative;
                        z-index: 1;
                    }
                    &:hover,
                    &:focus,
                    &:active{
                        &::before{
                            opacity: 0;
                        }
                    }
                }
            }
        }
    }
}

/* result-content */
.result-content{ 
    padding-bottom:45px; 
    padding-top:42px;
    @include mediaQuery("phablet", max) {
        padding-top:25px;
    }
    .result-content-inner{
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        border-radius: 4px;
        overflow: hidden;
        &.firstCol{
            @include mediaQuery("tabletWide", max) {
                margin-bottom: 25px;
            }
        }
    }
    .result-main-title{ 
        height: 87px;
        background: $colorWhite;
        border-radius: 4px 4px 0 0;
        @include flexCenter(row, space-between, center);
        padding: 0 11px 0 30px;
        @include mediaQuery("phablet", max) {
            padding: 0 11px 0 15px;
            height: 57px;
        }
        &.enlarge{
            height: auto;
        }
        .left-title-area{ 
            flex: 1;
            line-height:47px;
            font-size:18px;
            text-transform: uppercase;
            color:$colorSecondary;
            float:left;
            font-weight:700;
            position:relative;
            @include mediaQuery("phablet", max) {
                font-size: 16px;
            }
            span{
                vertical-align: -2px;
                margin-right: 7px;
                svg{
                    vertical-align: initial;
                    path{
                        fill: $colorSecondary;
                    }
                }
                &.recent-search-icon{
                    svg{
                        vertical-align: inherit;
                    }
                }
            }
            &.enlarge{
                font-size: 27px;
                font-weight: 600;
                text-transform: capitalize;
                padding-top: 15px;
                line-height: 1.3;
                @include mediaQuery("phablet", max) {
                    font-size: 20px;
                }
                .date-sec{
                    font-size: 14px;
                    color: #000;
                    margin: 8px 0 15px 0;
                    a{
                        background-color: rgba(0, 0, 0, 0.1);
                        color: #000;
                        border-radius: 100px;
                        padding: 1px 10px;
                        font-weight: 600;
                    }
                }
            }
        }
        a{
            &.see-all{
                line-height:40px;
                font-size:16px;
                color:$colorSecondary;
                float:right;
                padding:0 11px 0 0;
                position:relative;
            }
            &:hover{
                &.see-all{
                    text-decoration: underline;
                }
            }
        }
    }
}
/* extension-area */
.extension-area{
    ul{
        margin:0;
        padding:0;
        list-style:none;
        li{
            background: $colorWhite;
            padding:13px;
            padding-left: 30px;
            @include mediaQuery("phablet", max) {
                padding-left: 15px;
            }
            &:nth-of-type(2n+1) {
                background: darken($colorWhite, 3%);
            }
            &:last-child{
                border-radius: 0 0 4px 4px;
            }
            .left-extension{
                font-size:20px;
                color:$colorSecondary;
                line-height:37px;
                font-weight: 500;
                position: relative;
                overflow: hidden;
                text-overflow: ellipsis;
                padding-right: 10px;
                @include mediaQuery("phablet", max) {
                    line-height: 30px;
                    font-size:18px;
                }
                &.green{
                    color:$colorGreen;
                }
                &.red{
                    color:$colorRed;
                }
                &.wd-icon{
                    padding-left: 35px;
                    position: relative;
                    .icon{
                        width: 25px;
                        height: 25px;
                        border-radius: 4px;
                        background-position: center center;
                        background-repeat: no-repeat;
                        background-size: cover;
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        img{
                            width: 25px;
                            max-width: 100%;
                            height: auto;
                        }
                    }
                }
            }
            .right-by-wo-btn{
                border:none;
                border-radius:4px;
                margin-left: 16px;
                padding:0;
                width: 120px;
                height: 37px;
                line-height:37px;
                font-size:17px;
                font-weight:700;
                text-transform: uppercase;
                float:right;
                background:#fff;
                text-align: center;
                color: $colorWhite;
                position: relative;
                overflow: hidden;
                @include mediaQuery("phablet", max) {
                    width: 80px;
                    height: 30px;
                    line-height: 30px;
                    font-size: 14px;
                }
                span, svg{
                    position: relative;
                    z-index: 1;
                }
                &:hover,
                &:focus{
                    &:before{
                        opacity: 0;
                    }
                }
                &.green-by-btn{
                    background:  #03BE80;
                    &:before{
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        background:  #03BE80;
                        background: -moz-linear-gradient(left,   #03BE80 1%, #00954F 100%);
                        background: -webkit-linear-gradient(left,   #03BE80 1%,#00954F 100%);
                        background: linear-gradient(to right,   #03BE80 1%,#00954F 100%);
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #03BE80', endColorstr='#00954F',GradientType=1 );
                        @include transition();
                    }
                }
                &.blue-by-btn{
                    background:  $gradBlueLight;
                    &:before{
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        background: $gradBlueLight;
                        background: -moz-linear-gradient(left,  $gradBlueLight 0%, $gradBlueDark 100%);
                        background: -webkit-linear-gradient(left,  $gradBlueLight 0%,$gradBlueDark 100%);
                        background: linear-gradient(to right,  $gradBlueLight 0%,$gradBlueDark 100%);
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$gradBlueLight', endColorstr='$gradBlueDark',GradientType=1 );
                        @include transition();
                    }
                }
                &.grey-by-btn{
                    background:  lighten($colorSecondaryLight, 10%);
                    &:before{
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        background:  lighten($colorSecondaryLight, 10%);
                        background: -moz-linear-gradient(left,   lighten($colorSecondaryLight, 10%) 1%, lighten($colorPlaceholder, 10%) 100%);
                        background: -webkit-linear-gradient(left,   lighten($colorSecondaryLight, 10%) 1%,lighten($colorPlaceholder, 10%) 100%);
                        background: linear-gradient(to right,   lighten($colorSecondaryLight, 10%) 1%,lighten($colorPlaceholder, 10%) 100%);
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' lighten($colorSecondaryLight, 10%)', endColorstr='lighten($colorPlaceholder, 10%)',GradientType=1 );
                        @include transition();
                    }
                }
                &.red-by-btn{
                    background:  #CF0031;
                    &:before{
                        content: '';
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        background:  #CF0031;
                        background: -moz-linear-gradient(left,   #CF0031 1%, #B70932 100%);
                        background: -webkit-linear-gradient(left,   #CF0031 1%,#B70932 100%);
                        background: linear-gradient(to right,   #CF0031 1%,#B70932 100%);
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#B70932',GradientType=1 );
                        @include transition();
                    }
                }
            }
            .right-price-per-yr{
                line-height:32px;
                font-size: 14px;
                font-weight: 500;
                float:right;
                color:$colorSecondary;
                @include mediaQuery("phablet", max) {
                    line-height: 30px;
                    font-size: 18px;
                }
            }
            .right-price-offer{
                line-height:37px;
                font-size: 16px;
                font-weight: 500;
                float:right;
                color:#707070;
                margin-right: 6px;
                @include mediaQuery("phablet", max) {
                    line-height: 30px;
                    font-size: 14px;
                    margin-right: 0;
                }
            }
        }
    }
}
.homepageColumnsArea{
    padding-top: 45px;
    @include mediaQuery("tabletWide", max) {
        padding-top: 30px;
    }
    .homepageColumnsMain{
        background: $colorWhite;
        text-align: center;
        min-height: calc(100% - 30px);
        position: relative;
        @include flexCenter(column, flex-start, initial);
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        border-radius: 4px;
        padding: 40px;
        margin-bottom: 30px;
        @include mediaQuery("tablet", max) {
            padding: 25px;
            box-shadow: 0px 10px 10px transparentize($colorBlack, .9);
        }
        .homepageColumnsIcon{
            height: 151px;
            @include flexCenter(row, center, end);
            margin-bottom: 50px;
            @include mediaQuery("tabletWide", max) {
                height: 120px;
                max-width: 150px;
                margin: 0 auto 40px;
            }
            @include mediaQuery("tablet", max) {
                height: 100px;
                max-width: 120px;
                margin: 0 auto 25px;
            }
            img{
                @include mediaQuery("tabletWide", max) {
                    max-height: 100%;
                    max-width: 100%;
                }
            }
        }
        .homepageColumnsMain-title{
            font-size: 27px;
            font-weight: 800;
            text-align: center;
            color: $colorBlack;
            text-transform: uppercase;
            min-height: 77px;
            margin: 0 0 32px 0;
            @include mediaQuery("tabletWide", max) {
                min-height: 55px;
                margin: 0 0 20px 0;
            }
            @include mediaQuery("tablet", max) {
                min-height: 45px;
                margin: 0 0 10px 0;
                font-size: 18px;
            }
        }
        p{
            font-size: 20px;
            line-height: 27px;
            text-align: center;
            color: $colorBlack;
            font-weight: 300;
            margin-bottom: 0;
            @include mediaQuery("tabletWide", max) {
                font-size: 18px;
            }
            @include mediaQuery("tablet", max) {
                font-size: 16px;
                line-height: 25px;
            }
        }
    }
}
.homepageFaqsAreaMain{
    padding-top: 40px;
    @include mediaQuery("tablet", max) {
        padding-top: 10px;
    }
    .homepageFaqsArea{
        position: relative;
        background: $colorWhite;
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        border-radius: 4px;
        padding: 40px;
        overflow: hidden;
        @include mediaQuery("tablet", max) {
            padding: 25px;
        }
        &:before{
            content: '';
            width: 982px;
            height: 980px;
            position: absolute;
            left: -210px;
            top: -660px;
            background: url(../images/leftbg.svg) center center no-repeat;
            background-size: 100%;
            opacity: .8;
        }
        .faq-main-title{
            font-size: 65px;
            font-weight: 800;
            color: $colorBlack;
            text-align: center;
            margin-bottom: 50px;
            &.whoisSearchTitle{
                font-size: 48px;
            }
            @include mediaQuery("tabletWide", max) {
                font-size: 45px;
                margin-bottom: 40px;
            }
            @include mediaQuery("tablet", max) {
                font-size: 40px;
                margin-bottom: 20px;
            }
        }
    }
}
footer{
    padding-top: 50px;
    .footer-inner{
        background: #111;
        padding-bottom: 50px;
        .navigationSection{
            padding: 50px 0;
            @include mediaQuery("tablet", max) {
                padding: 20px 0;
            }
            .footer-nav-title{
                font-size: 20px;
                font-weight: 700;
                color: $colorWhite;
                margin-bottom: 25px;
            }
            ul{
                margin: 0;
                padding: 0;
                list-style: none;
                li{
                    a{
                        display: block;
                        color: $colorWhite;
                        opacity: .5;
                        line-height: 30px;
                        &:hover, &:focus{
                            opacity: .8;
                        }
                    }
                }
                @include mediaQuery("tabletWide", max) {
                    margin-bottom: 25px;
                }
            }
            p{
                color: $colorWhite;
                opacity: .5;
            }
            .footerLogo{
                margin-bottom: 25px;
            }
            .footerCopyright{
                max-width: 350px;
                color: $colorWhite;
                opacity: .5;
                line-height: 25px;
                a{
                    color: $colorGreen;
                    &:hover, &:focus{
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
.whoisText{
    white-space: pre-wrap;
    word-wrap: break-word;
    background: $colorWhite;
    font-size: 15px;
    color: $colorBlack;
    line-height: 28px;
    @include mediaQuery("tablet", max) {
        font-size: 14px;
    }
}
.domainDnsSection{
    border-top: lighten($colorSecondaryLight, 15%) 1px solid;
    background: $colorWhite;
    padding: 30px;
    @include clearfix;
    @include mediaQuery("tablet", max) {
        padding: 20px;
    }
    h2{
        font-size: 20px;
        font-weight: 700;
        color: $colorBlack;
    }
    h3{
        font-size: 18px;
        font-weight: 500;
        color: $colorBlack;
        margin-bottom: 20px;
    }
    h4{
        font-size: 15px;
        font-weight: 700;
        color: $colorPrimary;
    }
    .dnsBlock{
        border: lighten($colorSecondaryLight, 15%) 1px solid;
        margin-bottom: 20px;
        &:last-child{
            margin-bottom: none;
        }
        .dnsBlockRow{
            padding: 10px;
            color: $colorBlack;
            border-bottom: lighten($colorSecondaryLight, 15%) 1px solid;
            &:last-child{
                border-bottom: none;
            }
        }
    }
}
.contactDivider{
    border-radius: 100px;
    max-width: 500px;
    margin: 0 auto;
    background: darken($colorWhite, 10%);
    height: 8px;
    margin-bottom: 40px;
}
.contactInputs{
    flex: 1;
    background: $colorWhite;
    border-radius: 4px;
    padding: 10px 18px;
    box-shadow: 0px 7px 15px transparentize($colorBlack, .9);
    height: 50px;
    border: none;
    font-size: 18px;
    color: $colorSecondary;
    &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: $colorPlaceholder;
        opacity: 1; /* Firefox */
    }

    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: $colorPlaceholder;
    }

    &::-ms-input-placeholder { /* Microsoft Edge */
        color: $colorPlaceholder;
    }
    &:focus{
        box-shadow: 0px 7px 15px transparentize($colorBlack, .9);
    }
}
.contactTextarea{
    flex: 1;
    background: $colorWhite;
    border-radius: 4px;
    padding: 10px 18px;
    box-shadow: 0px 7px 15px transparentize($colorBlack, .9);
    border: none;
    font-size: 18px;
    color: $colorSecondary;
    &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: $colorPlaceholder;
        opacity: 1; /* Firefox */
    }

    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
        color: $colorPlaceholder;
    }

    &::-ms-input-placeholder { /* Microsoft Edge */
        color: $colorPlaceholder;
    }
    &:focus{
        box-shadow: 0px 7px 15px transparentize($colorBlack, .9);
    }
}
.contactButton{
    position: relative;
    width: 200px;
    height: 60px;
    border-radius: 4px;
    @include flexCenter();
    text-transform: uppercase;
    font-size: 22px;
    font-weight: 700;
    box-shadow: 0px 7px 15px transparentize($colorBlack, .9);
    background: $gradBlueLight;
    color: $colorWhite;
    float: right;
    &:hover{
        color: $colorWhite;
        background: $gradBlueLight;
        .blockGrad{
            opacity: 0;
        }
    }
    span{
        position: relative;
        z-index: 1;
    }
}
.contactImage{
    max-width: 450px;
    margin: 0 auto;
    min-height: 100%;
    padding: 10px 0 0 0;
    @include mediaQuery("tabletWide", max) {
        display: none;
    }
}
.domain-options{
    // display:  flex;
    // flex-wrap: wrap;
    padding-top: 20px;
    .domain-options-inner-main{
        position: relative;
    }
    .domain-options-inner{
        // overflow: hidden;
        padding: 0;
        // flex: 1;
        // width: 1%;
        margin: 0 80px 0 0;
        list-style: none;
        li{
            float: left;
            padding-right: 8px;
        }
    }
    .receiver-domains{
        overflow: hidden;
        padding: 0;
        margin: 0;
        list-style: none;
        position: absolute;
        width: 200px;
        top: 39px;
        right: 0;
        background-color: #fff;
        box-shadow: 2px 20px 25px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        z-index: 9;
        visibility: hidden;
        opacity: 0;
        &.show{
            visibility: visible;
            opacity: 1;
        }
        li{
            float: left;
            padding-right: 4px;
            padding-left: 4px;
            width: calc(50% - 4px);
            .domain-ckbx{
                width: 100%;
                justify-content: flex-start;
            }
        }
    }
    .receiver-more-btn{
        position: absolute;
        right: 0;
        top: 0;
        .nav-show-more-btn{
            user-select: none;
            cursor: pointer;
            width: 80px;
            right: 0;
            top: 0;
            height: 31px;
            border-radius: 4px;
            background-color: #0072CF;
            color: #fff;
            text-align: center;
            line-height: 31px;
            font-size: 16px;
            font-weight: 700;
        }
    }
    .custom-control-input, .custom-control-label{
        cursor: pointer;
    }
    .domain-ckbx{
        background: $colorWhite;
        box-shadow: 2px 7px 14px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        padding: 5px 16px;
        // margin-right: 15px;
        margin-bottom: 5px;
        float: left;
        display: flex;
        justify-content: center;
        align-items: center;
        @include mediaQuery("tabletWide", max){
            padding: 3px 10px;
            margin-right: 5px;
            font-size: 13px;
            svg{
                width: 10px;
            }
        }
        @include mediaQuery("phablet", max) {
            padding: 3px 7px;
        }
        label.custom-control-label{
            font-size: 16px;
            font-weight: 700;
            @include mediaQuery("phablet", max) {
                font-size: 14px;
            }
        }
    }
}
.recent-sidebar-searches{
    margin-bottom: 20px;
    a{
        color: $colorDarkGrey;
        font-size: 17px;
        font-weight: 500;
        display: block;
        line-height: 31px;
        padding: 7px 10px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        &:nth-of-type(2n+1) {
            background-color: rgba(0, 0, 0, 0.05);
        }
        &:hover{
            background-color: rgba(0, 0, 0, 0.075);
        }
    }
}
.recent-search-btn{
    position: relative;
    width: 140px;
    height: 45px;
    border-radius: 4px;
    @include flexCenter();
    font-size: 17px;
    font-weight: 600;
    background: $gradBlueLight;
    color: $colorWhite;
    &:hover{
        background: $gradBlueLight;
        color: $colorWhite;
        .blockGrad{
            opacity: 0;
        }
    }
    span{
        position: relative;
        z-index: 1;
    }
}
.whoisSearchResultsMain{
    position: relative;
    z-index: 1;
    .whoisSearchResultsBlock{
        min-height: calc(100% - 25px);
        margin-bottom: 25px;
        .thumb{
            height: 200px;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .domain-name-section{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .domain-name{
                flex: 1;
                width: 1%;
                font-size: 17px;
                color: $colorDarkGrey;
                font-weight: 600;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                a{
                    color: $colorDarkGrey;
                }
            }
        }
        .right-by-wo-btn{
            border:none;
            border-radius:4px;
            margin-left: 16px;
            padding:0;
            width: 120px;
            height: 37px;
            line-height:37px;
            font-size:17px;
            font-weight:700;
            text-transform: uppercase;
            float:right;
            background:#fff;
            text-align: center;
            color: $colorWhite;
            position: relative;
            overflow: hidden;
            @include mediaQuery("phablet", max) {
                width: 80px;
                height: 30px;
                line-height: 30px;
                font-size: 14px;
            }
            span, svg{
                position: relative;
                z-index: 1;
            }
            &:hover,
            &:focus{
                &:before{
                    opacity: 0;
                }
            }
            &.red-by-btn{
                background:  #CF0031;
                &:before{
                    content: '';
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    left: 0;
                    top: 0;
                    background:  #CF0031;
                    background: -moz-linear-gradient(left,   #CF0031 1%, #B70932 100%);
                    background: -webkit-linear-gradient(left,   #CF0031 1%,#B70932 100%);
                    background: linear-gradient(to right,   #CF0031 1%,#B70932 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=' #CF0031', endColorstr='#B70932',GradientType=1 );
                    @include transition();
                }
            }
        }
    }
}
.whoispageination{
        text-align: center;
        margin: 0;
        padding: 30px 0 0 0;
        list-style: none;
        display: flex;
        justify-content: center;
    a{
        background-color: #f7f7f7;
        padding: 0 18px;
        line-height: 40px;
        font-size: 16px;
        font-weight: 600;
        color: #707070;
        display: flex;
        border-radius: 4px;
        margin: 0 3px;
        justify-content: center;
        align-items: center;
        &:hover,
        &:focus,
        &:active,
        &.active{
            background-color: $colorPrimary;
            color: $colorWhite;
        }
        &.disabled{
            pointer-events: none;
            opacity: .65;
        }
    }
}
.alert-danger{
    color: #fff;
    background-color: #CF0031;
    border-color: #CF0031;
}
.alert-success{
    color: #fff;
    background-color: #03BE80;
    border-color: #03BE80;
}
.showmore-btn{
    position: relative;
    width: 170px;
    height: 55px;
    border-radius: 4px;
    @include flexCenter();
    text-transform: uppercase;
    font-size: 17px;
    font-weight: 700;
    margin: 0 auto;
    box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
    background: $gradBlueLight;
    color: $colorWhite;
    &:hover{
        color: $colorWhite;
        background: $gradBlueLight;
        .blockGrad{
            opacity: 0;
        }
    }
    span{
        position: relative;
        z-index: 1;
    }
    @include mediaQuery("phablet", max) {
        width: 130px;
        height: 45px;
        font-size: 15px;
    }
}
.footer-ad, .middle-ad, .header-ad{
    padding: 0 15px;
    img{
        max-width: 100%;
        height: auto;
    }
}
.start-button {
    padding-bottom: 50px;
    a {
        z-index: 1;
        color: #fff;
        text-transform: uppercase;
        text-decoration: none;
        display: block;
        margin: 0 auto;
        text-align: center;
        font-size: 40px;
        font-size: 4rem;
        font-weight: 500;
        box-sizing: border-box;
        position: relative;
        left: 0;
        padding: 0;
        width: 180px;
        height: 180px;
        line-height: 182px;
        border-radius: 180px;
        transform: translateY(20px) translateX(0);
        transform-origin: center center;
        border-width: 0;
        .start-ring {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            box-sizing: border-box;
            border: 2px #707070 solid;
            opacity: 0;
            animation-name: start-ring;
            animation-delay: 3.5s;
            animation-duration: 3.5s;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
          }
          .start-background {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            box-sizing: border-box;
            opacity: 0;
            background-color: #0072CF;
          }
          .start-border {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            box-sizing: border-box;
            background-color: #0072CF;
            border: 2px transparent solid;
            background-origin: border-box;
            background-clip: content-box,border-box;
            background-image: linear-gradient(#008CFF,#0072CF),linear-gradient(to bottom,#707070,#707070);
            animation-name: start-heartbeat;
            animation-delay: 3.5s;
            animation-duration: 3.5s;
            animation-iteration-count: infinite;
            animation-timing-function: ease-out;
          }
          .start-text {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 100%;
            box-sizing: border-box;
            white-space: nowrap;
            opacity: 1;
            font-weight: 900;
          }
      }
}
@keyframes start-heartbeat {
    0% {
        transform: scale(1);
    }
    8.333% {
        transform: scale(0.989);
    }
    16.667% {
        transform: scale(1);
    }
}
@keyframes start-ring {
    0% {
        opacity: 0;
        transform: scale(1);
    }
    12.5% {
        opacity: 0;
        transform: scale(0.995);
    }
    16.667% {
        opacity: 1;
    }
    50% {
        opacity: 0;
        transform: scale(1.3);
    }
}

.gauge-wrapper {
    visibility: visible;
    position: relative;
    width: 328px;
    margin: 0 auto;
}
.gauge-background {
    height: 328px;
}
#arc-background {
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -164px;
}
.gauge {
    position: absolute;
    top: 0;
    left: 164px;
    margin-left: -164px;
    width: 328px;
    height: 328px;
    z-index: 1;
}
.gauge .gauge-group {
    opacity: 1;
}
.gauge .gauge-ping-progress {
    position: absolute;
    top: 0;
    left: 0;
    opacity: .1;
    -ms-transform: scale(.5);
    -o-transform: scale(.5);
    -moz-transform: scale(.5);
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    -o-transform-origin: 0 0;
    transform-origin: 0 0;
}
.gauge .connecting-message, .gauge .latency-message {
    text-align: center;
    color: #9193a8;
    position: absolute;
    left: 0;
    bottom: 20px;
    font-size: 14px;
    font-size: 1.1999999998rem;
    width: 100%;
    font-weight: 500;
    opacity: 0;
}
.gauge .connecting-message, .gauge .latency-message {
    font-size: 14px;
    font-size: 1.4rem;
}
.gauge .gauge-group-speed {
    position: absolute;
    top: 0;
    left: 0;
    width: 328px;
    height: 328px;
}
.gauge .gauge-speed-needle {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 328px;
    height: 328px;
    -webkit-transform-origin: 164px 164px;
    -moz-transform-origin: 164px 164px;
    -ms-transform-origin: 164px 164px;
    -o-transform-origin: 164px 164px;
    transform-origin: 164px 164px;
    transform: rotateZ(-132deg);
    transform: rotateZ(-31.053deg);
    opacity: 1;
}
.gauge .increments .increment {
    z-index: 1;
    position: absolute;
    width: 20%;
    height: 2em;
    text-align: center;
    opacity: 1;
    font-size: 18px;
    font-weight: 700;
}
.gauge .increments .increment-0 {
    top: 69.2073171%;
    left: 20.1219512%;
    text-align: left;
}
.gauge .increments .increment-1 {
    top: 52.7439024%;
    left: 12.5%;
    text-align: left;
}
.gauge .increments .increment-2 {
    top: 31.7073171%;
    left: 14.6341463%;
    text-align: left;
}
.gauge .increments .increment-3 {
    top: 16.7682927%;
    left: 26.8292683%;
    text-align: left;
}
.gauge .increments .increment-4 {
    top: 9.7560976%;
    left: 0;
    width: 100%;
}
.gauge .increments .increment-5 {
    top: 16.7682927%;
    right: 26.8292683%;
    text-align: right;
}
.gauge .increments .increment-6 {
    top: 31.7073171%;
    right: 14.6341463%;
    text-align: right;
}
.gauge .increments .increment-7 {
    top: 52.7439024%;
    right: 12.5%;
    text-align: right;
}
.gauge .increments .increment-8 {
    top: 69.2073171%;
    right: 20.1219512%;
    text-align: right;
}
.gauge .gauge-unit-icon {
    display: block;
    position: absolute;
    left: 0;
    bottom: 20px;
    font-size: 14px;
    font-size: 1.1999999998rem;
    width: 100%;
    font-weight: 500;
    color: rgb(145,147,168);
    line-height: 1.4;
    text-align: center;
    cursor: default;
    font-size: 1.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
}
.gauge .gauge-icon {
    position: relative;
    top: 3px;
    font-size: larger;
    margin-right: .2em;
    animation: pulse infinite 1s 0s;
    animation-direction: alternate;
    animation-timing-function: ease-in-out;
    width: 18px;
    height: 18px;
}
/**/
.result-container-data {
    max-width: 720px;
    margin: 0 auto 40px;
    display: flex;
    justify-content: space-around;
}
.result-item-container-align-right {
    text-align: right;
    position: relative;
}
.result-item-container-align-center {
    text-align: center;
}
.result-item-container-align-left {
    text-align: left;
}
.result-container-data .result-item-container {
    min-width: 100px;
}
.result-item-container .result-item {
    display: inline-block;
    width: auto;
}
.result-label {
    font-size: 14px;
}
.svg-icon {
    width: 18px;
    height: 18px;
    position: relative;
    top: -1px;
    margin-right: 5px;
}
.result-item-ping .svg-icon path {
    color: #fff38e;
}
.result-item-download .svg-icon path{
    color: #6afff3;
}
.result-item-upload .svg-icon path{
    color: #bf71ff;
}
.result-data-unit {
    color: #9193a8;
    margin-top: -.8em;
    text-transform: none;
}
.result-item-first-class .result-data {
    display: block;
    line-height: 1.2;
    background-color: rgba(26,27,46,0);
    -webkit-transition: all 250ms ease-in-out;
    -moz-transition: all 250ms ease-in-out;
    -ms-transition: all 250ms ease-in-out;
    -o-transition: all 250ms ease-in-out;
    transition: all 250ms ease-in-out;
}
.result-item-first-class .result-data.u-align-left {
    padding-left: 1.7em;
}
.result-data-large {
    font-size: 50px;
    color: $colorGrey;
}
.blog-title{
    font-size: 48px;
    font-weight: 800;
    color: $colorBlack;
    text-align: center;
    margin-bottom: 40px;
    @include mediaQuery("tabletWide", max) {
        font-size: 43px;
        margin-bottom: 40px;
    }
    @include mediaQuery("tablet", max) {
        font-size: 39px;
        margin-bottom: 20px;
    }
    @include mediaQuery("phablet", max) {
        font-size: 30px;
    }
}
.blogColumn-section{
    padding-top: 0;
    .blogColumnsMain{
        background: $colorWhite;
        min-height: calc(100% - 25px);
        margin-bottom: 25px;
        position: relative;
        @include flexCenter(column, flex-start, initial);
        box-shadow: 2px 20px 25px transparentize($colorBlack, .9);
        border-radius: 5px;
        padding: 20px;
        @include mediaQuery("tablet", max) {
            padding: 10px;
            box-shadow: 0px 10px 10px transparentize($colorBlack, .9);
        }
        .homepageColumnsImage{
            height: 200px;
            margin-bottom: 15px;
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            border-radius: 5px;
            @include mediaQuery("tabletWide", max) {
                margin-bottom: 10px;
            }
        }
        h2{
            font-size: 25px;
            font-weight: 700;
            color: $colorBlack;
            text-transform: uppercase;
            margin: 0 0 15px 0;
            @include mediaQuery("tabletWide", max) {
                margin: 0 0 10px 0;
                font-size: 20px;
            }
            a{
                color: $colorBlack;
                &:hover, &:focus, &:active{
                    color: $colorPrimary;
                }
            }
        }
        .date-sec{
            font-size: 14px;
            color: $colorBlack;
            margin: 0 0 15px 0;
            a{
                background-color: rgba(0, 0, 0, 0.1);
                color: $colorBlack;
                border-radius: 100px;
                padding: 1px 10px;
                font-weight: 600;
            }
        }
        p{
            font-size: 18px;
            line-height: 1.3;
            color: $colorBlack;
            font-weight: 400;
            margin-bottom: 0;
            @include mediaQuery("tabletWide", max) {
                font-size: 16px;
            }
            @include mediaQuery("tablet", max) {
                font-size: 15px;
                line-height: 25px;
            }
        }
    }
}
.blog-single-image{
    margin-bottom: 20px;
    position: relative;
}
.blog-single-inner{
    max-width: 1260px;
    margin: 0 auto;
}

.homeFaqsRow, .domainDnsSection.homeFaqsRow{
    position: relative;
    z-index: 1;
    .homefaq-subtitle{
        font-size: 27px;
        font-weight: 600;
        color: $colorBlack;
        margin: 0 0 25px 0;
        @include mediaQuery("tabletWide", max) {
            font-size: 20px;
            margin: 0 0 13px 0;
        }
    }
    p{
        font-size: 18px;
        color: $colorBlack;
        font-weight: 300;
        margin: 0 0 32px 0;
        @include mediaQuery("tabletWide", max) {
            font-size: 16px;
        }
        a{
            color: $colorPrimary;
        }
    }
    ul{
        margin-bottom: 30px;
        li{
            font-size: 18px;
            color: $colorBlack;
            font-weight: 300;
            a{
                color: $colorPrimary;
            }
        }
    }
}
body.theme-dark{
    background-color: $colorDark;
    .headerBottom{
        .headerBottomBlocks{
            background-color: $colorDarkLighter;
            .headerBottomBlockIcon{
                svg{
                    path{
                        fill: $colorWhite;
                    }
                }
            }
            h2{
                color: $colorWhite;
            }
        }
    }
    .searchSection{
        .inputFiled{
            background-color: $colorDarkLight;
            color: $colorWhite;
        }
        .searchInput{
            .searchCross{
                background-color: $colorDark;
            }
        }
    }
    .homepageColumnsArea{
        .homepageColumnsMain{
            background-color: $colorDarkLighter;
            h2, p{
                color: $colorWhite;
            }
        }
    }
    .homepageFaqsAreaMain{
        .homepageFaqsArea{
            background-color: $colorDarkLighter;
            h1, h4, p{
                color: $colorWhite;
            }
        }
    }
    footer{
        .footer-inner{
            background-color: $colorDarkLighter;
        }
    }
    .contactDivider{
        background-color: $colorDarkLight;
    }
    .result-content{
        .result-main-title{
            background-color: $colorDarkLighter;
            .left-title-area{
                color: $colorWhite;
                span{
                    svg{
                        path{
                            fill: $colorWhite;
                        }
                    }
                }
                &.enlarge{
                    .date-sec{
                        color: #fff;
                        a{
                            background-color: rgba(255, 255, 255, 0.1);
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
    .extension-area{
        ul{
            li{
                background-color: $colorDarkLighter;
                &:nth-of-type(2n+1){
                    background-color: $colorDarkLight;
                }
                .left-extension, .left-extension.text-dark{
                    color: $colorWhite !important;
                }
            }
        }
    }
    .homeFaqsRow, .domainDnsSection.homeFaqsRow{
        .homefaq-subtitle, p{
            color: $colorWhite;
        }
        ul{
            li{
                color: $colorWhite;
            }
        }
    }
    .domainDnsSection{
        background-color: $colorDarkLighter;
        border-top-color: $colorLightGrey;
        h2, h3{
            color: $colorWhite;
        }
        .dnsBlock{
            border-color: $colorLightGrey;
            .dnsBlockRow{
                border-color: $colorLightGrey;
                color: $colorWhite;
            }
        }
    }
    .domain-options{
        .domain-ckbx{
            background-color: $colorDarkLighter;
        }
    }
    label.custom-control-label{
        color: $colorWhite;
    }
    .whoisText{
        background-color: $colorDarkLighter;
        color: $colorWhite;
    }
    .recent-sidebar-searches{
        a{
            color: $colorWhite;
        }
    }
    .blog-title{
        color: $colorWhite;
    }
    .blogColumn-section{
        .blogColumnsMain{
            background-color: $colorDarkLighter;
            h2{
                a{
                    color: $colorWhite;
                    &:hover, &:focus, &:active{
                        color: $colorPrimary;
                    }
                }
            }
            .date-sec{
                color: $colorWhite;
                a{
                    background-color: rgba(255, 255, 255, 0.05);
                    color: $colorWhite;
                }
            }
            p{
                color: $colorWhite;
            }
        }
    }
}

.logos-sec{
    max-width: 100%;
    border-radius: 5px 5px 0 0;
    background-color: #fff;
    box-shadow: 2px 4px 15px rgba(0, 0, 0, 0.5);
    padding: 0 10px;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    overflow: auto;
    height: 50px;
    z-index: 9;
    display: flex;
    align-items: center;
    text-align: center;
    bottom: 0;
    a{
        margin: 6px 6px;
        display: block;
        min-width: 110px;
        max-width: 110px;
        img{
            max-width: 100%;
            height: auto;
        }
    }
}

