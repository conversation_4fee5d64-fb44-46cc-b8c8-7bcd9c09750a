<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-trademark me-2"></i>
                        <?= lang('brandable_checker') ?>
                    </h4>
                    <p class="text-muted mb-0">Evaluate domain brandability, memorability, and business potential</p>
                </div>
                <div class="card-body">
                    <form id="brandable-form" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="domain-input" 
                                           name="domain" 
                                           placeholder="Enter domain name (e.g., Spotify.com, Airbnb.com)"
                                           required>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        <?= lang('check_brandable') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Loading Spinner -->
                    <div id="loading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing brandability potential...</p>
                    </div>

                    <!-- Results Section -->
                    <div id="results" class="d-none">
                        <div class="row">
                            <!-- Score Overview -->
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h2 id="total-score" class="display-4 mb-0">0</h2>
                                        <p class="mb-0"><?= lang('total_score') ?></p>
                                        <small id="grade" class="opacity-75"></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Memorability -->
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="memorability-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('memorability_score') ?></p>
                                        <small class="opacity-75">Easy to Remember</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pronounceability -->
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3 id="pronounceability-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('pronounceability_score') ?></p>
                                        <small class="opacity-75">Easy to Say</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Uniqueness -->
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3 id="uniqueness-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('uniqueness_score') ?></p>
                                        <small class="opacity-75">Distinctive</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Metrics -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="versatility-score" class="text-purple mb-0">0</h4>
                                        <p class="mb-0"><?= lang('versatility_score') ?></p>
                                        <small class="text-muted">Industry Flexibility</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="estimated-value" class="text-success mb-0">-</h4>
                                        <p class="mb-0"><?= lang('estimated_value') ?></p>
                                        <small class="text-muted">Market Value Range</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Analysis -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-star me-2"></i>
                                            <?= lang('brand_potential') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="brand-potential" class="mb-3">
                                            <p class="text-muted">Analyzing brand potential...</p>
                                        </div>
                                        
                                        <h6>Industry Fit:</h6>
                                        <div id="industry-fit">
                                            <!-- Industry fit will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <?= lang('recommendations') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="recommendations">
                                            <!-- Recommendations will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="alert alert-danger d-none" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-text"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        What Makes a Domain Brandable?
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6><i class="fas fa-brain text-primary me-2"></i>Memorability</h6>
                            <ul class="list-unstyled small">
                                <li>• Short and sweet (6-12 characters)</li>
                                <li>• Easy to remember</li>
                                <li>• Good vowel-consonant balance</li>
                                <li>• No repetitive patterns</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-volume-up text-success me-2"></i>Pronounceability</h6>
                            <ul class="list-unstyled small">
                                <li>• Easy to say out loud</li>
                                <li>• Clear syllable structure</li>
                                <li>• No difficult combinations</li>
                                <li>• Flows naturally</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-gem text-info me-2"></i>Uniqueness</h6>
                            <ul class="list-unstyled small">
                                <li>• Creative and distinctive</li>
                                <li>• Not generic terms</li>
                                <li>• Modern suffixes (.ly, .io)</li>
                                <li>• Invented/coined words</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-arrows-alt text-warning me-2"></i>Versatility</h6>
                            <ul class="list-unstyled small">
                                <li>• Works across industries</li>
                                <li>• International appeal</li>
                                <li>• Abstract/metaphorical</li>
                                <li>• Future-proof</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Examples of Successful Brandable Domains:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <span class="badge bg-primary me-2">Spotify</span>
                                <span class="badge bg-primary me-2">Airbnb</span>
                                <span class="badge bg-primary me-2">Uber</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-success me-2">Dropbox</span>
                                <span class="badge bg-success me-2">Snapchat</span>
                                <span class="badge bg-success me-2">Pinterest</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-info me-2">Shopify</span>
                                <span class="badge bg-info me-2">Squarespace</span>
                                <span class="badge bg-info me-2">Mailchimp</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('brandable-form');
    const loading = document.getElementById('loading');
    const results = document.getElementById('results');
    const errorMessage = document.getElementById('error-message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const domain = document.getElementById('domain-input').value.trim();
        if (!domain) return;
        
        // Show loading, hide results and errors
        loading.classList.remove('d-none');
        results.classList.add('d-none');
        errorMessage.classList.add('d-none');
        
        // Make AJAX request
        fetch('<?= base_url('brandable-checker/query') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'domain=' + encodeURIComponent(domain)
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.add('d-none');
            
            if (data.type === 'success') {
                displayResults(data);
                results.classList.remove('d-none');
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            loading.classList.add('d-none');
            showError('An error occurred while processing your request.');
        });
    });
    
    function displayResults(data) {
        document.getElementById('total-score').textContent = Math.round(data.total_score);
        document.getElementById('grade').textContent = data.grade;
        document.getElementById('memorability-score').textContent = Math.round(data.memorability_score);
        document.getElementById('pronounceability-score').textContent = Math.round(data.pronounceability_score);
        document.getElementById('uniqueness-score').textContent = Math.round(data.uniqueness_score);
        document.getElementById('versatility-score').textContent = Math.round(data.versatility_score);
        document.getElementById('estimated-value').textContent = data.estimated_value;
        
        // Display brand potential
        const brandPotentialContainer = document.getElementById('brand-potential');
        brandPotentialContainer.innerHTML = `<div class="alert alert-info mb-0">${data.brand_potential}</div>`;
        
        // Display industry fit
        const industryFitContainer = document.getElementById('industry-fit');
        if (data.industry_fit && data.industry_fit.length > 0) {
            industryFitContainer.innerHTML = data.industry_fit.map(industry => 
                `<span class="badge bg-secondary me-2 mb-2">${industry}</span>`
            ).join('');
        } else {
            industryFitContainer.innerHTML = '<span class="text-muted">Universal appeal</span>';
        }
        
        // Display recommendations
        const recommendationsContainer = document.getElementById('recommendations');
        if (data.recommendations && data.recommendations.length > 0) {
            recommendationsContainer.innerHTML = data.recommendations.map(rec => 
                `<div class="alert alert-info py-2 mb-2">
                    <i class="fas fa-lightbulb me-2"></i>${rec}
                </div>`
            ).join('');
        } else {
            recommendationsContainer.innerHTML = '<p class="text-muted">No specific recommendations</p>';
        }
    }
    
    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('d-none');
    }
});
</script>
