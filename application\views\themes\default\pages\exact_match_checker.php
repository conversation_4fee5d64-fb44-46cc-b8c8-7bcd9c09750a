<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bullseye me-2"></i>
                        <?= lang('exact_match_checker') ?>
                    </h4>
                    <p class="text-muted mb-0">Analyze exact match keywords and commercial value potential</p>
                </div>
                <div class="card-body">
                    <form id="exact-match-form" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="domain-input" 
                                           name="domain" 
                                           placeholder="Enter domain name (e.g., Insurance.com, Lawyer.com)"
                                           required>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        <?= lang('check_exact_match') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Loading Spinner -->
                    <div id="loading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing exact match keywords...</p>
                    </div>

                    <!-- Results Section -->
                    <div id="results" class="d-none">
                        <div class="row">
                            <!-- Score Overview -->
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h2 id="total-score" class="display-4 mb-0">0</h2>
                                        <p class="mb-0"><?= lang('total_score') ?></p>
                                        <small id="grade" class="opacity-75"></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SEO Score -->
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="seo-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('seo_score') ?></p>
                                        <small class="opacity-75">Search Engine Value</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Commercial Value -->
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3 id="commercial-value" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('commercial_value') ?></p>
                                        <small class="opacity-75">Business Potential</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Keyword Density -->
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3 id="keyword-density" class="mb-0">0%</h3>
                                        <p class="mb-0"><?= lang('keyword_density') ?></p>
                                        <small class="opacity-75">Keyword Focus</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Results -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-target me-2"></i>
                                            <?= lang('exact_matches') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="exact-matches">
                                            <p class="text-muted">No exact matches found</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-search me-2"></i>
                                            <?= lang('partial_matches') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="partial-matches">
                                            <p class="text-muted">No partial matches found</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Value and Recommendations -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-dollar-sign me-2"></i>
                                            <?= lang('estimated_value') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <h4 id="estimated-value" class="text-success mb-0">-</h4>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <?= lang('recommendations') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="recommendations">
                                            <!-- Recommendations will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="alert alert-danger d-none" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-text"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Understanding Exact Match Domains
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>High-Value Categories</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-shield-alt text-primary me-2"></i>Insurance & Finance</li>
                                <li><i class="fas fa-balance-scale text-primary me-2"></i>Legal Services</li>
                                <li><i class="fas fa-heartbeat text-primary me-2"></i>Medical & Health</li>
                                <li><i class="fas fa-home text-primary me-2"></i>Real Estate</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>Medium-Value Categories</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-laptop text-info me-2"></i>Technology</li>
                                <li><i class="fas fa-briefcase text-info me-2"></i>Business Services</li>
                                <li><i class="fas fa-plane text-info me-2"></i>Travel & Tourism</li>
                                <li><i class="fas fa-graduation-cap text-info me-2"></i>Education</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>Benefits of Exact Match</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-search text-success me-2"></i>Better SEO Rankings</li>
                                <li><i class="fas fa-mouse-pointer text-success me-2"></i>Higher Click-Through Rates</li>
                                <li><i class="fas fa-dollar-sign text-success me-2"></i>Premium PPC Performance</li>
                                <li><i class="fas fa-brain text-success me-2"></i>Instant Recognition</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Examples of High-Value Exact Match Domains:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <span class="badge bg-success me-2">Insurance.com</span>
                                <span class="badge bg-success me-2">Lawyer.com</span>
                                <span class="badge bg-success me-2">Doctor.com</span>
                            </div>
                            <div class="col-md-6">
                                <span class="badge bg-info me-2">Travel.com</span>
                                <span class="badge bg-info me-2">Hotel.com</span>
                                <span class="badge bg-info me-2">Car.com</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('exact-match-form');
    const loading = document.getElementById('loading');
    const results = document.getElementById('results');
    const errorMessage = document.getElementById('error-message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const domain = document.getElementById('domain-input').value.trim();
        if (!domain) return;
        
        // Show loading, hide results and errors
        loading.classList.remove('d-none');
        results.classList.add('d-none');
        errorMessage.classList.add('d-none');
        
        // Make AJAX request
        fetch('<?= base_url('exact-match-checker/query') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'domain=' + encodeURIComponent(domain)
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.add('d-none');
            
            if (data.type === 'success') {
                displayResults(data);
                results.classList.remove('d-none');
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            loading.classList.add('d-none');
            showError('An error occurred while processing your request.');
        });
    });
    
    function displayResults(data) {
        document.getElementById('total-score').textContent = Math.round(data.total_score);
        document.getElementById('grade').textContent = data.grade;
        document.getElementById('seo-score').textContent = Math.round(data.seo_score);
        document.getElementById('commercial-value').textContent = Math.round(data.commercial_value);
        document.getElementById('keyword-density').textContent = data.keyword_density + '%';
        document.getElementById('estimated-value').textContent = data.estimated_value;
        
        // Display exact matches
        const exactContainer = document.getElementById('exact-matches');
        if (data.exact_matches && data.exact_matches.length > 0) {
            exactContainer.innerHTML = data.exact_matches.map(match => 
                `<div class="mb-2">
                    <span class="badge bg-success me-2">${match.keyword}</span>
                    <span class="text-muted">${match.category} (${Math.round(match.value)} points)</span>
                </div>`
            ).join('');
        } else {
            exactContainer.innerHTML = '<p class="text-muted">No exact keyword matches found</p>';
        }
        
        // Display partial matches
        const partialContainer = document.getElementById('partial-matches');
        if (data.partial_matches && data.partial_matches.length > 0) {
            partialContainer.innerHTML = data.partial_matches.map(match => 
                `<div class="mb-2">
                    <span class="badge bg-info me-2">${match.keyword}</span>
                    <span class="text-muted">${match.category} (${Math.round(match.value)} points)</span>
                </div>`
            ).join('');
        } else {
            partialContainer.innerHTML = '<p class="text-muted">No partial keyword matches found</p>';
        }
        
        // Display recommendations
        const recommendationsContainer = document.getElementById('recommendations');
        if (data.recommendations && data.recommendations.length > 0) {
            recommendationsContainer.innerHTML = data.recommendations.map(rec => 
                `<div class="alert alert-info py-2 mb-2">
                    <i class="fas fa-lightbulb me-2"></i>${rec}
                </div>`
            ).join('');
        } else {
            recommendationsContainer.innerHTML = '<p class="text-muted">No specific recommendations</p>';
        }
    }
    
    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('d-none');
    }
});
</script>
