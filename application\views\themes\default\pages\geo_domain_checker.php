<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-globe-americas me-2"></i>
                        <?= lang('geo_domain_checker') ?>
                    </h4>
                    <p class="text-muted mb-0">Evaluate geographic domain value and commercial potential</p>
                </div>
                <div class="card-body">
                    <form id="geo-domain-form" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="domain-input" 
                                           name="domain" 
                                           placeholder="Enter domain name (e.g., ChicagoPlumbing.com)"
                                           required>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        <?= lang('check_geo_domain') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Loading Spinner -->
                    <div id="loading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing geographic domain value...</p>
                    </div>

                    <!-- Results Section -->
                    <div id="results" class="d-none">
                        <div class="row">
                            <!-- Score Overview -->
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h2 id="total-score" class="display-4 mb-0">0</h2>
                                        <p class="mb-0">Total Score</p>
                                        <small id="grade" class="opacity-75"></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Geographic Score -->
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="geographic-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('geographic_score') ?></p>
                                        <small class="opacity-75">Max: 20 points</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Commercial Potential -->
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3 id="commercial-potential" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('commercial_potential') ?></p>
                                        <small class="opacity-75">Max: 25 points</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Results -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-map-marker-alt me-2"></i>
                                            <?= lang('detected_cities') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="detected-cities">
                                            <p class="text-muted">No cities detected</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-dollar-sign me-2"></i>
                                            <?= lang('estimated_value') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <h4 id="estimated-value" class="text-success mb-3">-</h4>
                                        <div id="recommendations">
                                            <!-- Recommendations will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="alert alert-danger d-none" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-text"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        How Geographic Domain Evaluation Works
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Geographic Scoring (Max: 20 points)</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-star text-warning me-2"></i>Major International Cities: 20 points</li>
                                <li><i class="fas fa-star text-warning me-2"></i>Major National Cities: 15 points</li>
                                <li><i class="fas fa-star text-warning me-2"></i>Regional Hubs: 10 points</li>
                                <li><i class="fas fa-star text-warning me-2"></i>Local Cities: 5 points</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Commercial Potential (Max: 25 points)</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-chart-line text-success me-2"></i>High Commercial Keywords: 25 points</li>
                                <li><i class="fas fa-chart-line text-info me-2"></i>Medium Commercial Keywords: 15 points</li>
                                <li><i class="fas fa-chart-line text-secondary me-2"></i>Low Commercial Keywords: 5 points</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Examples of High-Value Geographic Domains:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <span class="badge bg-success me-2">ChicagoPlumbing.com</span>
                                <span class="badge bg-success me-2">LondonLegal.com</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-info me-2">MiamiHotels.com</span>
                                <span class="badge bg-info me-2">VegasWedding.com</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-warning me-2">RiyadhRealestate.com</span>
                                <span class="badge bg-warning me-2">DubaiTravel.com</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('geo-domain-form');
    const loading = document.getElementById('loading');
    const results = document.getElementById('results');
    const errorMessage = document.getElementById('error-message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const domain = document.getElementById('domain-input').value.trim();
        if (!domain) return;
        
        // Show loading, hide results and errors
        loading.classList.remove('d-none');
        results.classList.add('d-none');
        errorMessage.classList.add('d-none');
        
        // Make AJAX request
        fetch('<?= base_url('geo-domain-checker/query') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'domain=' + encodeURIComponent(domain)
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.add('d-none');
            
            if (data.type === 'success') {
                displayResults(data);
                results.classList.remove('d-none');
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            loading.classList.add('d-none');
            showError('An error occurred while processing your request.');
        });
    });
    
    function displayResults(data) {
        document.getElementById('total-score').textContent = data.total_score;
        document.getElementById('grade').textContent = data.grade;
        document.getElementById('geographic-score').textContent = data.geographic_score;
        document.getElementById('commercial-potential').textContent = data.commercial_potential;
        document.getElementById('estimated-value').textContent = data.estimated_value;
        
        // Display detected cities
        const citiesContainer = document.getElementById('detected-cities');
        if (data.detected_cities && data.detected_cities.length > 0) {
            citiesContainer.innerHTML = data.detected_cities.map(city => 
                `<div class="mb-2">
                    <span class="badge bg-primary me-2">${city.city}</span>
                    <span class="text-muted">${city.importance} (${city.score} points)</span>
                </div>`
            ).join('');
        } else {
            citiesContainer.innerHTML = '<p class="text-muted">No major cities detected</p>';
        }
        
        // Display recommendations
        const recommendationsContainer = document.getElementById('recommendations');
        if (data.recommendations && data.recommendations.length > 0) {
            recommendationsContainer.innerHTML = data.recommendations.map(rec => 
                `<div class="alert alert-info py-2 mb-2">
                    <i class="fas fa-lightbulb me-2"></i>${rec}
                </div>`
            ).join('');
        } else {
            recommendationsContainer.innerHTML = '<p class="text-muted">No specific recommendations</p>';
        }
    }
    
    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('d-none');
    }
});
</script>
