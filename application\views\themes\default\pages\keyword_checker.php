<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>
                        <?= lang('keyword_checker') ?>
                    </h4>
                    <p class="text-muted mb-0">Analyze keyword potential, search volume, and SEO/PPC opportunities</p>
                </div>
                <div class="card-body">
                    <form id="keyword-form" class="mb-4">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           class="form-control form-control-lg" 
                                           id="domain-input" 
                                           name="domain" 
                                           placeholder="Enter domain name (e.g., HealthInsurance.com, تأمين.com)"
                                           required>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search me-2"></i>
                                        <?= lang('check_keyword') ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Loading Spinner -->
                    <div id="loading" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Analyzing keyword potential...</p>
                    </div>

                    <!-- Results Section -->
                    <div id="results" class="d-none">
                        <div class="row">
                            <!-- Score Overview -->
                            <div class="col-md-2">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h2 id="total-score" class="display-4 mb-0">0</h2>
                                        <p class="mb-0"><?= lang('total_score') ?></p>
                                        <small id="grade" class="opacity-75"></small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Search Volume -->
                            <div class="col-md-2">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="search-volume-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('search_volume_score') ?></p>
                                        <small class="opacity-75">Monthly Searches</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Competition -->
                            <div class="col-md-2">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3 id="competition-score" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('competition_score') ?></p>
                                        <small class="opacity-75">Difficulty Level</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SEO Potential -->
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3 id="seo-potential" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('seo_potential') ?></p>
                                        <small class="opacity-75">Organic Ranking</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- PPC Potential -->
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3 id="ppc-potential" class="mb-0">0</h3>
                                        <p class="mb-0"><?= lang('ppc_potential') ?></p>
                                        <small class="opacity-75">Paid Advertising</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Keyword Density -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="keyword-density" class="text-purple mb-0">0%</h4>
                                        <p class="mb-0"><?= lang('keyword_density') ?></p>
                                        <small class="text-muted">Keyword Focus Ratio</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="estimated-value" class="text-success mb-0">-</h4>
                                        <p class="mb-0"><?= lang('estimated_value') ?></p>
                                        <small class="text-muted">Market Value Range</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Analysis -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-tags me-2"></i>
                                            <?= lang('detected_keywords') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="detected-keywords">
                                            <p class="text-muted">No keywords detected</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <?= lang('recommendations') ?>
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="recommendations">
                                            <!-- Recommendations will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="error-message" class="alert alert-danger d-none" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="error-text"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Understanding Keyword Domain Value
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6><i class="fas fa-chart-line text-success me-2"></i>High Volume Keywords</h6>
                            <ul class="list-unstyled small">
                                <li>• Insurance (1M+ searches)</li>
                                <li>• Loans (900K+ searches)</li>
                                <li>• Mortgage (800K+ searches)</li>
                                <li>• Lawyer (700K+ searches)</li>
                                <li>• تأمين (200K+ searches)</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-chart-bar text-info me-2"></i>Medium Volume Keywords</h6>
                            <ul class="list-unstyled small">
                                <li>• Dental (150K+ searches)</li>
                                <li>• Property (120K+ searches)</li>
                                <li>• Restaurant (100K+ searches)</li>
                                <li>• Fitness (90K+ searches)</li>
                                <li>• عقار (160K+ searches)</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-trophy text-warning me-2"></i>Competition Levels</h6>
                            <ul class="list-unstyled small">
                                <li>• <span class="badge bg-danger">Very High</span> Insurance, Loans</li>
                                <li>• <span class="badge bg-warning">High</span> Health, Travel</li>
                                <li>• <span class="badge bg-info">Medium</span> Dental, Legal</li>
                                <li>• <span class="badge bg-success">Low</span> Fitness, Beauty</li>
                            </ul>
                        </div>
                        <div class="col-md-3">
                            <h6><i class="fas fa-dollar-sign text-primary me-2"></i>Value Factors</h6>
                            <ul class="list-unstyled small">
                                <li>• Search volume importance</li>
                                <li>• Commercial intent level</li>
                                <li>• Competition difficulty</li>
                                <li>• PPC cost per click</li>
                                <li>• Language market size</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Examples of High-Value Keyword Domains:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <span class="badge bg-danger me-2">Insurance.com</span>
                                <span class="badge bg-danger me-2">Loans.com</span>
                                <span class="badge bg-danger me-2">Mortgage.com</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-warning me-2">Health.com</span>
                                <span class="badge bg-warning me-2">Travel.com</span>
                                <span class="badge bg-warning me-2">Doctor.com</span>
                            </div>
                            <div class="col-md-4">
                                <span class="badge bg-info me-2">تأمين.com</span>
                                <span class="badge bg-info me-2">عقار.com</span>
                                <span class="badge bg-info me-2">سيارات.com</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('keyword-form');
    const loading = document.getElementById('loading');
    const results = document.getElementById('results');
    const errorMessage = document.getElementById('error-message');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const domain = document.getElementById('domain-input').value.trim();
        if (!domain) return;
        
        // Show loading, hide results and errors
        loading.classList.remove('d-none');
        results.classList.add('d-none');
        errorMessage.classList.add('d-none');
        
        // Make AJAX request
        fetch('<?= base_url('keyword-checker/query') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'domain=' + encodeURIComponent(domain)
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.add('d-none');
            
            if (data.type === 'success') {
                displayResults(data);
                results.classList.remove('d-none');
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            loading.classList.add('d-none');
            showError('An error occurred while processing your request.');
        });
    });
    
    function displayResults(data) {
        document.getElementById('total-score').textContent = Math.round(data.total_score);
        document.getElementById('grade').textContent = data.grade;
        document.getElementById('search-volume-score').textContent = Math.round(data.search_volume_score);
        document.getElementById('competition-score').textContent = Math.round(data.competition_score);
        document.getElementById('seo-potential').textContent = Math.round(data.seo_potential);
        document.getElementById('ppc-potential').textContent = Math.round(data.ppc_potential);
        document.getElementById('keyword-density').textContent = data.keyword_density + '%';
        document.getElementById('estimated-value').textContent = data.estimated_value;
        
        // Display detected keywords
        const keywordsContainer = document.getElementById('detected-keywords');
        if (data.detected_keywords && data.detected_keywords.length > 0) {
            keywordsContainer.innerHTML = data.detected_keywords.map(keyword => {
                const difficultyColor = {
                    'very_high': 'danger',
                    'high': 'warning', 
                    'medium': 'info',
                    'low': 'success',
                    'very_low': 'secondary'
                };
                
                return `<div class="mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${keyword.keyword}</h6>
                        <span class="badge bg-${difficultyColor[keyword.difficulty]}">${keyword.difficulty.replace('_', ' ')}</span>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Volume: ${keyword.volume.toLocaleString()}/month</small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Category: ${keyword.category.replace('_', ' ')}</small>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Language: ${keyword.language}</small>
                        </div>
                    </div>
                </div>`;
            }).join('');
        } else {
            keywordsContainer.innerHTML = '<p class="text-muted">No high-value keywords detected</p>';
        }
        
        // Display recommendations
        const recommendationsContainer = document.getElementById('recommendations');
        if (data.recommendations && data.recommendations.length > 0) {
            recommendationsContainer.innerHTML = data.recommendations.map(rec => 
                `<div class="alert alert-info py-2 mb-2">
                    <i class="fas fa-lightbulb me-2"></i>${rec}
                </div>`
            ).join('');
        } else {
            recommendationsContainer.innerHTML = '<p class="text-muted">No specific recommendations</p>';
        }
    }
    
    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('d-none');
    }
});
</script>
