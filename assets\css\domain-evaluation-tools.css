/* Domain Evaluation Tools Custom Styles */

/* Score Cards */
.score-card {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.score-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.score-display {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

/* Grade Badges */
.grade-excellent { color: #28a745; }
.grade-good { color: #17a2b8; }
.grade-average { color: #ffc107; }
.grade-poor { color: #dc3545; }

/* Progress Bars for Scores */
.score-progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
    margin-top: 8px;
}

.score-progress-bar {
    height: 100%;
    transition: width 0.8s ease;
    border-radius: 4px;
}

.progress-excellent { background: linear-gradient(90deg, #28a745, #20c997); }
.progress-good { background: linear-gradient(90deg, #17a2b8, #6f42c1); }
.progress-average { background: linear-gradient(90deg, #ffc107, #fd7e14); }
.progress-poor { background: linear-gradient(90deg, #dc3545, #e83e8c); }

/* Keyword Tags */
.keyword-tag {
    display: inline-block;
    padding: 4px 12px;
    margin: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.keyword-tag:hover {
    transform: scale(1.05);
    color: white;
    text-decoration: none;
}

.keyword-tag.high-value {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.keyword-tag.medium-value {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.keyword-tag.low-value {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* City Detection Cards */
.city-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    transition: transform 0.3s ease;
}

.city-card:hover {
    transform: translateX(5px);
}

.city-importance {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Recommendation Cards */
.recommendation-card {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border: none;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #ff6b6b;
}

.recommendation-card .fas {
    color: #ff6b6b;
}

/* Loading Animation */
.evaluation-loading {
    text-align: center;
    padding: 40px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Value Estimation */
.value-estimate {
    font-size: 1.8rem;
    font-weight: 700;
    color: #28a745;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.value-range {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: normal;
}

/* Brandability Metrics */
.brandability-meter {
    position: relative;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.brandability-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 1s ease;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3, #54a0ff);
}

/* Competition Level Indicators */
.competition-very-high { color: #dc3545; font-weight: bold; }
.competition-high { color: #fd7e14; font-weight: bold; }
.competition-medium { color: #ffc107; font-weight: bold; }
.competition-low { color: #28a745; font-weight: bold; }
.competition-very-low { color: #17a2b8; font-weight: bold; }

/* Search Volume Indicators */
.volume-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.volume-bars {
    display: flex;
    gap: 2px;
    align-items: end;
}

.volume-bar {
    width: 4px;
    background: #007bff;
    border-radius: 2px;
}

.volume-bar.bar-1 { height: 8px; }
.volume-bar.bar-2 { height: 12px; }
.volume-bar.bar-3 { height: 16px; }
.volume-bar.bar-4 { height: 20px; }
.volume-bar.bar-5 { height: 24px; }

/* Responsive Design */
@media (max-width: 768px) {
    .score-display {
        font-size: 2rem;
    }
    
    .value-estimate {
        font-size: 1.4rem;
    }
    
    .keyword-tag {
        font-size: 0.75rem;
        padding: 3px 8px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .score-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .recommendation-card {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: #e2e8f0;
    }
    
    .city-card {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    }
}

/* Animation for Results Appearance */
.results-container {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tooltip Styles */
.evaluation-tooltip {
    position: relative;
    cursor: help;
}

.evaluation-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}
