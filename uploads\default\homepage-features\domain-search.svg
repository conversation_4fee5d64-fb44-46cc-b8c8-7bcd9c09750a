<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="151.222" height="151.573" viewBox="0 0 151.222 151.573">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b37cff"/>
      <stop offset="1" stop-color="#f895e7"/>
    </linearGradient>
  </defs>
  <g id="Availability" transform="translate(-490.967 -451.336)">
    <path id="Path_2013" data-name="Path 2013" d="M1456.507,2410.815l-3.737,33.52h43.361l-5.648-33.52Z" transform="translate(-910.644 -1848.784)" fill="#f3f3f3" stroke="#707070" stroke-width="1"/>
    <path id="Path_2014" data-name="Path 2014" d="M1280.2,2968.21h-63.462c-1.724,0-3.019-1.228-2.686-2.546l.814-3.22a2.626,2.626,0,0,1,2.576-1.719l61.18-1.908a2.705,2.705,0,0,1,2.781,1.667l1.468,5.13C1283.254,2966.945,1281.953,2968.21,1280.2,2968.21Z" transform="translate(-685.374 -2365.801)" fill="#f3f3f3" stroke="#707070" stroke-width="1"/>
    <path id="Path_2015" data-name="Path 2015" d="M556.714,989v76.768a11.646,11.646,0,0,0,11.346,11.642c28.039.723,100.259,2.476,126.95,2.083a8.569,8.569,0,0,0,8.436-8.325c.515-17.834,1.351-60.72-1.743-80.63a8.818,8.818,0,0,0-8.432-7.464c-23.494-.752-101.6-3.138-128.283-2.539A8.465,8.465,0,0,0,556.714,989Z" transform="translate(-65.247 -499.273)" fill="#f3f3f3" stroke="#707070" stroke-width="1"/>
    <path id="Path_2016" data-name="Path 2016" d="M641.917,1038.4v72.935a6.747,6.747,0,0,0,6.646,6.747l122.992,1.839a6.567,6.567,0,0,0,6.665-6.542l.249-72.283a4.8,4.8,0,0,0-4.686-4.819l-126.537-3.079A5.2,5.2,0,0,0,641.917,1038.4Z" transform="translate(-145.633 -549.045)" fill="#fff" stroke="#707070" stroke-width="1"/>
    <circle id="Ellipse_7" data-name="Ellipse 7" cx="2.302" cy="2.302" r="2.302" transform="translate(509.443 497.858) rotate(-80.783)" fill="#a8a8a8"/>
    <circle id="Ellipse_8" data-name="Ellipse 8" cx="2.302" cy="2.302" r="2.302" transform="translate(509.076 506.889) rotate(-22.5)" fill="#fff"/>
    <circle id="Ellipse_9" data-name="Ellipse 9" cx="2.302" cy="2.302" r="2.302" transform="translate(509.782 518.014)" fill="#fff"/>
    <circle id="Ellipse_10" data-name="Ellipse 10" cx="2.302" cy="2.302" r="2.302" transform="translate(509.443 534.4) rotate(-80.783)" fill="#fff"/>
    <circle id="Ellipse_11" data-name="Ellipse 11" cx="2.302" cy="2.302" r="2.302" transform="translate(509.782 543.707)" fill="#fff"/>
    <circle id="Ellipse_12" data-name="Ellipse 12" cx="2.302" cy="2.302" r="2.302" transform="translate(509.782 555.888)" fill="#fff"/>
    <rect id="Rectangle_25" data-name="Rectangle 25" width="2.664" height="81.267" rx="1.332" transform="translate(520.06 486.955)" fill="#008cff" opacity="0.7"/>
    <rect id="Rectangle_26" data-name="Rectangle 26" width="67.944" height="2.664" rx="1.332" transform="translate(526.53 501.8)" fill="#707070"/>
    <rect id="Rectangle_27" data-name="Rectangle 27" width="25.186" height="2.664" rx="1.332" transform="translate(526.53 510.437)" fill="#707070"/>
    <rect id="Rectangle_28" data-name="Rectangle 28" width="25.186" height="2.664" rx="1.332" transform="translate(598.852 527.509)" fill="#707070"/>
    <rect id="Rectangle_29" data-name="Rectangle 29" width="25.186" height="2.664" rx="1.332" transform="translate(565.337 535.986)" fill="#707070"/>
    <rect id="Rectangle_30" data-name="Rectangle 30" width="15.543" height="2.664" rx="1.332" transform="translate(555.84 543.707)" fill="#707070"/>
    <rect id="Rectangle_31" data-name="Rectangle 31" width="15.543" height="2.664" rx="1.332" transform="translate(527.482 550.749)" fill="#707070"/>
    <rect id="Rectangle_32" data-name="Rectangle 32" width="25.186" height="2.664" rx="1.332" transform="translate(527.482 543.707)" fill="#707070"/>
    <rect id="Rectangle_33" data-name="Rectangle 33" width="41.617" height="2.664" rx="1.332" transform="translate(574.808 543.707)" fill="#707070"/>
    <rect id="Rectangle_34" data-name="Rectangle 34" width="36.478" height="2.664" rx="1.332" transform="translate(584.388 518.984)" fill="#707070"/>
    <rect id="Rectangle_35" data-name="Rectangle 35" width="36.478" height="2.664" rx="1.332" transform="translate(526.53 535.986)" fill="#707070"/>
    <rect id="Rectangle_36" data-name="Rectangle 36" width="54.495" height="2.664" rx="1.332" transform="translate(555.078 510.437)" fill="#707070"/>
    <rect id="Rectangle_37" data-name="Rectangle 37" width="54.495" height="2.664" rx="1.332" transform="translate(526.53 518.984)" fill="#707070"/>
    <rect id="Rectangle_38" data-name="Rectangle 38" width="67.944" height="2.664" rx="1.332" transform="translate(526.53 527.509)" fill="#707070"/>
    <path id="Path_2020" data-name="Path 2020" d="M1255.954,1192.187l-2.837-2.1.056-.662,3.148-1.59-.081.955-2.17,1.044,1.964,1.4Z" transform="translate(-722.279 -694.942)"/>
    <path id="Path_2021" data-name="Path 2021" d="M1315.133,1175.107l-2.837,6.011-1.087-.092,2.837-6.011Z" transform="translate(-777.087 -682.85)"/>
    <path id="Path_2022" data-name="Path 2022" d="M1381.617,1197.827l2.183-1.051-1.977-1.394.081-.955,2.838,2.095-.056.662-3.149,1.6Z" transform="translate(-843.437 -701.164)"/>
    <path id="Path_2023" data-name="Path 2023" d="M1212.775,2346.775l-2.837-2.1.056-.662,3.148-1.59-.081.955-2.17,1.044,1.964,1.4Z" transform="translate(-681.541 -1784.255)"/>
    <path id="Path_2024" data-name="Path 2024" d="M1271.954,2329.695l-2.837,6.011-1.087-.092,2.837-6.012Z" transform="translate(-736.349 -1772.163)"/>
    <path id="Path_2025" data-name="Path 2025" d="M1338.438,2352.414l2.183-1.052-1.977-1.394.081-.956,2.838,2.095-.056.662-3.149,1.6Z" transform="translate(-802.7 -1790.476)"/>
    <path id="Path_2026" data-name="Path 2026" d="M2374.184,962.389a.371.371,0,0,0-.359.264q-.088.288-.168.579a.371.371,0,1,0,.716.2q.078-.282.163-.562a.372.372,0,0,0-.352-.48Z" transform="translate(-1779.456 -482.245)" fill="url(#linear-gradient)"/>
    <path id="Path_2027" data-name="Path 2027" d="M2359.189,1005.732a.371.371,0,0,1,.295.435c-.073.381-.137.769-.191,1.154a.372.372,0,0,1-.736-.1c.056-.4.122-.8.2-1.19a.371.371,0,0,1,.368-.3A.367.367,0,0,1,2359.189,1005.732Zm-.434,3.087a.372.372,0,0,1,.349.393c-.023.387-.037.781-.041,1.169a.371.371,0,0,1-.375.368h0a.372.372,0,0,1-.368-.375c0-.4.018-.807.042-1.206a.372.372,0,0,1,.375-.35Zm.365,3.463c.027.388.063.78.109,1.165a.372.372,0,0,1-.738.087c-.047-.4-.085-.8-.113-1.2a.372.372,0,0,1,.345-.4h.029A.372.372,0,0,1,2359.119,1012.282Zm.407,3.041c.076.38.162.764.257,1.141a.371.371,0,1,1-.72.182c-.1-.389-.188-.785-.266-1.176a.372.372,0,0,1,.292-.437.342.342,0,0,1,.077-.007A.372.372,0,0,1,2359.527,1015.324Zm.8,2.963c.124.367.26.736.4,1.1a.372.372,0,0,1-.691.274c-.147-.372-.288-.753-.417-1.132a.371.371,0,0,1,.232-.471.367.367,0,0,1,.123-.02A.372.372,0,0,1,2360.323,1018.287Zm1.176,2.835c.172.348.355.7.544,1.037a.371.371,0,1,1-.649.362c-.2-.351-.385-.711-.562-1.07a.372.372,0,1,1,.667-.329Zm1.535,2.66c.216.323.442.645.674.958a.372.372,0,0,1-.6.443c-.239-.322-.473-.654-.7-.987a.372.372,0,0,1,.618-.413Zm1.866,2.438c.256.293.522.583.792.862a.372.372,0,0,1-.535.516c-.277-.288-.552-.587-.816-.889a.371.371,0,1,1,.559-.489Zm2.165,2.177c.292.258.593.511.9.753a.372.372,0,1,1-.464.58c-.312-.25-.623-.511-.923-.776a.371.371,0,0,1,.492-.557Zm2.425,1.88c.322.218.653.43.984.631a.371.371,0,1,1-.385.635c-.342-.207-.683-.426-1.015-.651a.371.371,0,0,1,.212-.679A.367.367,0,0,1,2369.49,1030.276Zm24.958,1.411a.371.371,0,0,1-.152.5c-.354.19-.717.373-1.077.544a.372.372,0,0,1-.319-.672c.35-.166.7-.343,1.045-.527a.371.371,0,0,1,.5.152Zm-22.311.141c.347.174.7.342,1.056.5a.371.371,0,1,1-.3.68c-.366-.161-.732-.334-1.089-.514a.371.371,0,1,1,.333-.664Zm19.494,1.194a.372.372,0,0,1-.216.479c-.374.141-.757.276-1.139.4a.372.372,0,0,1-.229-.707c.371-.12.742-.25,1.1-.387a.37.37,0,0,1,.479.216Zm-16.671,0c.367.128.741.248,1.111.357a.372.372,0,1,1-.21.713c-.382-.112-.767-.236-1.146-.368a.372.372,0,1,1,.245-.7Zm13.7.956a.372.372,0,0,1-.277.447c-.391.092-.788.175-1.181.247a.372.372,0,0,1-.134-.731c.381-.07.766-.151,1.146-.24a.355.355,0,0,1,.089-.01A.372.372,0,0,1,2388.662,1033.98Zm-10.749-.137c.378.079.765.149,1.15.21a.372.372,0,0,1-.115.734c-.4-.062-.8-.135-1.187-.217a.371.371,0,0,1,.152-.727Zm7.682.7a.371.371,0,0,1-.332.407c-.349.036-.7.064-1.052.085l-.152.009a.371.371,0,1,1-.038-.742l.146-.008c.339-.02.683-.048,1.021-.083l.042,0A.372.372,0,0,1,2385.6,1034.544Zm-4.644-.273c.387.029.78.049,1.168.059a.372.372,0,0,1-.019.743c-.4-.011-.805-.031-1.2-.061a.372.372,0,0,1,.031-.742Z" transform="translate(-1764.998 -523.132)" fill="#707070"/>
    <path id="Path_2028" data-name="Path 2028" d="M3015.511,1438.155a.371.371,0,0,0-.212.065q-.241.164-.487.323a.372.372,0,1,0,.4.624q.253-.164.5-.334a.372.372,0,0,0-.206-.679Z" transform="translate(-2384.215 -931.113)" fill="url(#linear-gradient)"/>
    <path id="Path_2029" data-name="Path 2029" d="M3200.847,1164.711a.371.371,0,0,0-.368.3q-.054.288-.115.574a.372.372,0,1,0,.727.155q.063-.295.118-.592a.372.372,0,0,0-.362-.44Z" transform="translate(-2559.429 -673.129)" fill="#707070"/>
    <path id="Path_2030" data-name="Path 2030" d="M2578.5,660.469a.372.372,0,0,1-.01.743h0l-.214,0c-.31,0-.625,0-.938.009a.372.372,0,0,1-.022-.743c.322-.01.647-.013.967-.01Zm1.926.111c.393.038.789.087,1.178.144a.372.372,0,0,1-.108.735c-.377-.056-.761-.1-1.142-.14a.372.372,0,0,1,.039-.741Zm-4.629.357a.371.371,0,0,1-.329.409c-.379.041-.764.092-1.142.151a.372.372,0,0,1-.115-.734c.39-.061.787-.113,1.178-.156l.043,0A.371.371,0,0,1,2575.794,660.937Zm7.7.144c.385.088.772.187,1.15.294a.372.372,0,0,1-.2.715c-.367-.1-.742-.2-1.115-.285a.372.372,0,0,1,.086-.734A.379.379,0,0,1,2583.5,661.08Zm-10.736.331a.372.372,0,0,1-.275.448c-.373.089-.747.188-1.114.295a.372.372,0,0,1-.208-.713c.378-.11.764-.212,1.148-.3a.371.371,0,0,1,.448.275Zm13.719.558c.369.136.739.283,1.1.438a.372.372,0,1,1-.291.684c-.351-.15-.711-.292-1.068-.424a.372.372,0,1,1,.258-.7Zm-16.666.3a.372.372,0,0,1-.216.479c-.358.136-.717.282-1.066.435a.372.372,0,1,1-.3-.681c.361-.157.73-.309,1.1-.449a.371.371,0,0,1,.479.216Zm19.51.964c.348.182.7.375,1.038.575a.372.372,0,1,1-.376.641c-.33-.194-.669-.381-1.006-.557a.372.372,0,1,1,.344-.659ZM2567,663.5a.371.371,0,0,1-.152.5c-.336.18-.674.371-1,.568a.372.372,0,1,1-.382-.637c.339-.2.686-.4,1.033-.586a.371.371,0,0,1,.5.152Zm24.984,1.351c.323.225.645.461.957.7a.372.372,0,0,1-.454.588c-.3-.233-.615-.462-.928-.681a.371.371,0,0,1,.425-.609Zm2.436,1.94c.292.264.582.539.861.818a.371.371,0,0,1-.524.526c-.271-.27-.552-.537-.835-.793a.371.371,0,1,1,.5-.551Zm2.172,2.233c.257.3.509.609.75.921a.372.372,0,0,1-.587.455c-.234-.3-.479-.6-.728-.893a.372.372,0,0,1,.564-.483Zm1.872,2.491c.217.33.428.67.628,1.009a.371.371,0,1,1-.64.377c-.194-.329-.4-.658-.609-.979a.371.371,0,1,1,.621-.407Zm1.54,2.711c.172.355.339.719.494,1.082a.372.372,0,0,1-.684.292c-.15-.352-.311-.705-.479-1.049a.371.371,0,1,1,.668-.325Zm1.18,2.885c.125.372.243.754.35,1.134a.372.372,0,0,1-.715.2c-.1-.369-.218-.739-.339-1.1a.371.371,0,0,1,.234-.471.377.377,0,0,1,.122-.019A.372.372,0,0,1,2601.188,677.108Zm.8,3.01c.077.387.145.78.2,1.17a.372.372,0,1,1-.735.109c-.056-.378-.122-.759-.2-1.134a.371.371,0,1,1,.729-.145Zm.411,3.087c.006.081.011.161.016.242.019.314.032.632.038.944a.371.371,0,1,1-.743.016c-.006-.3-.019-.611-.037-.916,0-.078-.01-.157-.015-.235a.371.371,0,0,1,.345-.4h.03A.372.372,0,0,1,2602.4,683.2Zm-.33,2.723a.372.372,0,0,1,.35.392c-.022.394-.055.793-.1,1.183a.371.371,0,1,1-.739-.078c.04-.379.071-.765.093-1.148a.371.371,0,0,1,.374-.35Z" transform="translate(-1960.263 -197.39)" fill="#707070"/>
    <path id="Path_2031" data-name="Path 2031" d="M2529.356,739.4a.371.371,0,0,0-.219.069q-.245.175-.487.357a.371.371,0,1,0,.446.594q.234-.176.472-.346a.372.372,0,0,0-.212-.674Z" transform="translate(-1925.559 -271.86)" fill="#707070"/>
    <path id="Path_2032" data-name="Path 2032" d="M2464.309,774.941a.289.289,0,0,0-.025.334l1.822,3.006a15.329,15.329,0,0,0-1.473,4.737l-3.2,1.444a.289.289,0,0,0-.169.29l.385,4.2a.289.289,0,0,0,.219.254l3.412.837a15.321,15.321,0,0,0,2.311,4.39l-1.244,3.288a.289.289,0,0,0,.085.324l3.242,2.7a.289.289,0,0,0,.334.025l3.006-1.822a15.314,15.314,0,0,0,4.738,1.474l1.444,3.2a.288.288,0,0,0,.29.169l4.2-.385a.289.289,0,0,0,.254-.219l.837-3.412a15.316,15.316,0,0,0,4.391-2.311l3.288,1.244a.288.288,0,0,0,.324-.085l2.7-3.242a.289.289,0,0,0,.025-.334l-1.822-3.007a15.307,15.307,0,0,0,1.474-4.737l3.2-1.444a.288.288,0,0,0,.169-.29l-.385-4.2a.289.289,0,0,0-.219-.254l-3.412-.837a15.313,15.313,0,0,0-2.311-4.39l1.245-3.288a.289.289,0,0,0-.086-.324l-3.241-2.7a.289.289,0,0,0-.334-.025l-3.006,1.822a15.311,15.311,0,0,0-4.738-1.474l-1.444-3.2a.289.289,0,0,0-.29-.169l-4.2.385a.289.289,0,0,0-.254.219l-.837,3.412a15.313,15.313,0,0,0-4.39,2.311l-3.288-1.245a.289.289,0,0,0-.324.085Z" transform="translate(-1862.118 -297.46)" fill="#03be80" stroke="#6a6a6a" stroke-width="1"/>
    <path id="Path_2033" data-name="Path 2033" d="M2723.966,1024.62a4.027,4.027,0,1,1-.6-.05A4.088,4.088,0,0,1,2723.966,1024.62Zm1.4,6.778a3.447,3.447,0,0,0-1.495-6.191h0a3.447,3.447,0,1,0-1.07,6.81,3.5,3.5,0,0,0,.51.042A3.421,3.421,0,0,0,2725.369,1031.4Z" transform="translate(-2105.567 -540.91)" fill="#03be80" stroke="#fff" stroke-width="1"/>
    <path id="Path_2034" data-name="Path 2034" d="M2619.754,914.571a10.376,10.376,0,0,0-1.049.043h0q-.211.019-.42.047a10.41,10.41,0,0,0-1.45.3l-.041.012c-.082.023-.164.048-.245.073a.148.148,0,0,0-.1.14.137.137,0,0,0,.007.046.148.148,0,0,0,.186.1,10.109,10.109,0,0,1,2.094-.418h0a10.034,10.034,0,0,1,10.851,8.637c.019.143.036.287.049.432a10.217,10.217,0,0,1,.043,1.031,10.027,10.027,0,0,1-20.011.8,10.014,10.014,0,0,1,.945-5.256.142.142,0,0,0,.015-.063.149.149,0,0,0-.282-.066,10.324,10.324,0,1,0,19.631,4.588,10.537,10.537,0,0,0-.043-1.061A10.338,10.338,0,0,0,2619.754,914.571Z" transform="translate(-2001.812 -437.13)" fill="#03be80" stroke="#fff" stroke-width="1"/>
    <path id="Path_2035" data-name="Path 2035" d="M1985.773,635.162a.207.207,0,0,0-.2.147q-.076.25-.144.505a.207.207,0,1,0,.4.106q.065-.247.139-.491a.207.207,0,0,0-.138-.259A.21.21,0,0,0,1985.773,635.162Z" transform="translate(-1413.183 -173.518)" fill="#707070"/>
    <path id="Path_2036" data-name="Path 2036" d="M1977.381,662.152a.207.207,0,0,1,.165.242c-.064.339-.116.684-.155,1.027a.207.207,0,0,1-.412-.047c.04-.353.094-.709.159-1.057a.207.207,0,0,1,.205-.169A.227.227,0,0,1,1977.381,662.152Zm-.268,2.089a.207.207,0,0,1,.2.215c-.006.149-.01.3-.011.45q0,.293.006.589a.207.207,0,1,1-.414.013q-.009-.3-.006-.606,0-.232.012-.463a.207.207,0,0,1,.209-.2Zm.263,2.291c.035.344.083.689.143,1.028a.207.207,0,1,1-.408.072c-.061-.349-.111-.7-.147-1.058a.207.207,0,0,1,.185-.227h.023A.208.208,0,0,1,1977.376,666.533Zm.36,2.044c.084.334.182.67.29,1a.207.207,0,1,1-.393.13c-.112-.337-.212-.682-.3-1.026a.207.207,0,0,1,.15-.252.217.217,0,0,1,.053-.006A.207.207,0,0,1,1977.736,668.577Zm.652,1.97c.132.319.277.637.433.946a.207.207,0,0,1-.37.186c-.16-.317-.309-.645-.445-.973a.207.207,0,0,1,.383-.159Zm.933,1.856c.177.3.367.589.565.872a.207.207,0,0,1-.339.238c-.2-.291-.4-.593-.581-.9a.207.207,0,0,1,.072-.284.2.2,0,0,1,.108-.029A.207.207,0,0,1,1979.321,672.4Zm1.192,1.7c.217.266.447.529.685.781a.207.207,0,1,1-.3.284c-.244-.26-.481-.53-.7-.8a.207.207,0,0,1,.321-.262Zm1.424,1.511c.253.232.518.46.79.675a.207.207,0,1,1-.258.325c-.279-.222-.552-.455-.812-.694a.207.207,0,1,1,.28-.305Zm1.626,1.291c.283.193.579.38.878.554a.207.207,0,1,1-.209.358c-.308-.179-.612-.371-.9-.57a.207.207,0,0,1,.234-.342Zm15.083.9a.207.207,0,0,1-.086.28c-.314.167-.637.324-.961.468a.207.207,0,0,1-.167-.379c.315-.139.629-.292.934-.454a.207.207,0,0,1,.28.085Zm-13.29.143c.31.152.629.293.949.421a.207.207,0,0,1-.155.385c-.328-.132-.657-.278-.976-.434a.207.207,0,0,1,.182-.372Zm11.375.737a.207.207,0,0,1-.125.265c-.333.119-.676.228-1.019.323a.207.207,0,1,1-.111-.4c.334-.092.667-.2.991-.314a.207.207,0,0,1,.265.125Zm-9.45.036c.329.1.666.2,1,.279a.207.207,0,1,1-.1.4c-.344-.083-.69-.179-1.029-.288a.207.207,0,1,1,.126-.395Zm7.428.556a.207.207,0,0,1-.163.244c-.348.07-.7.127-1.055.172a.207.207,0,1,1-.052-.411c.342-.043.687-.1,1.026-.167a.2.2,0,0,1,.043,0A.207.207,0,0,1,1994.709,679.277Zm-5.41-.071c.34.056.686.1,1.03.131a.207.207,0,0,1-.037.413c-.354-.032-.711-.077-1.06-.135a.207.207,0,0,1,.035-.412Zm3.324.364a.207.207,0,0,1-.2.218c-.313.017-.632.024-.946.021l-.123,0a.207.207,0,0,1,.006-.414h0l.119,0c.305,0,.615,0,.92-.02h.013A.207.207,0,0,1,1992.623,679.569Z" transform="translate(-1405.128 -198.979)" fill="#707070"/>
    <path id="Path_2037" data-name="Path 2037" d="M2371.806,922.676a.207.207,0,0,0-.119.036q-.21.143-.427.28a.207.207,0,1,0,.221.351q.223-.141.439-.288a.207.207,0,0,0-.115-.379Z" transform="translate(-1777.117 -444.777)" fill="#707070"/>
    <path id="Path_2038" data-name="Path 2038" d="M2486.071,754.662a.208.208,0,0,0-.206.171c-.029.168-.062.335-.1.5a.207.207,0,0,0,.405.086q.055-.256.1-.516a.207.207,0,0,0-.168-.24A.182.182,0,0,0,2486.071,754.662Z" transform="translate(-1885.237 -286.262)" fill="#707070"/>
    <path id="Path_2039" data-name="Path 2039" d="M2110.876,452.834c.35.012.7.036,1.049.073a.207.207,0,0,1,.184.228.2.2,0,0,1-.228.184c-.336-.035-.679-.059-1.02-.07a.207.207,0,0,1,.009-.414Zm-.837.2a.207.207,0,0,1-.2.215q-.163.006-.326.016c-.231.014-.464.033-.694.058a.207.207,0,1,1-.044-.412c.236-.025.476-.045.713-.06.112-.007.224-.012.336-.016h.01A.207.207,0,0,1,2110.039,453.036Zm2.927.018c.345.061.69.136,1.028.221a.207.207,0,1,1-.1.4c-.328-.083-.664-.155-1-.215a.207.207,0,0,1,.038-.411A.211.211,0,0,1,2112.966,453.054Zm-4.991.176a.207.207,0,0,1-.167.241c-.335.06-.671.133-1,.217a.207.207,0,1,1-.1-.4c.338-.086.684-.161,1.028-.223a.211.211,0,0,1,.039,0A.208.208,0,0,1,2107.975,453.229Zm7.027.34c.33.109.661.233.985.366a.207.207,0,0,1-.158.383c-.315-.13-.636-.249-.958-.356a.207.207,0,0,1,.067-.4A.217.217,0,0,1,2115,453.57Zm-9.043.143a.208.208,0,0,1-.131.262c-.323.107-.645.228-.958.357a.207.207,0,1,1-.159-.383c.322-.134.653-.258.986-.368a.213.213,0,0,1,.067-.011A.207.207,0,0,1,2105.959,453.713Zm10.984.658c.312.155.622.325.923.5a.207.207,0,1,1-.211.357c-.292-.173-.594-.337-.9-.488a.207.207,0,1,1,.185-.371Zm-12.91.109a.207.207,0,0,1-.092.278c-.3.152-.605.318-.9.491a.207.207,0,0,1-.212-.356c.3-.178.611-.348.923-.505a.205.205,0,0,1,.095-.022A.208.208,0,0,1,2104.033,454.479Zm14.719.961c.288.2.572.411.843.628a.207.207,0,0,1-.26.324c-.263-.212-.539-.417-.819-.611a.207.207,0,0,1,.12-.378A.2.2,0,0,1,2118.752,455.44Zm1.639,1.315c.257.238.508.488.746.742a.207.207,0,1,1-.3.283c-.231-.247-.475-.489-.725-.721a.207.207,0,0,1,.282-.3Zm1.437,1.534c.221.272.434.555.634.84a.207.207,0,0,1-.34.238c-.194-.278-.4-.553-.616-.816a.207.207,0,0,1,.322-.261Zm1.206,1.723c.179.3.35.611.507.922a.207.207,0,1,1-.37.187c-.153-.3-.319-.6-.493-.9a.207.207,0,0,1,.356-.213Zm.948,1.878c.135.322.259.654.37.984a.207.207,0,0,1-.393.132c-.108-.321-.229-.643-.359-.956a.207.207,0,0,1,.111-.271.2.2,0,0,1,.082-.016A.207.207,0,0,1,2123.982,461.89Zm.669,1.993c.087.338.163.683.225,1.027a.207.207,0,1,1-.408.074c-.061-.334-.135-.67-.219-1a.207.207,0,0,1,.149-.252.211.211,0,0,1,.053-.007A.207.207,0,0,1,2124.65,463.883Zm.378,2.067c.027.248.048.5.063.749q.009.15.015.3a.207.207,0,1,1-.414.017q-.006-.146-.014-.291c-.014-.242-.036-.486-.062-.728a.207.207,0,0,1,.183-.229h.025A.208.208,0,0,1,2125.028,465.95Zm-.118,1.886a.207.207,0,0,1,.2.214c-.011.35-.034.7-.07,1.049a.207.207,0,1,1-.412-.042c.035-.337.057-.68.068-1.02a.207.207,0,0,1,.209-.2Z" transform="translate(-1523.859 -1.498)" fill="#707070"/>
    <path id="Path_2040" data-name="Path 2040" d="M2080.372,498.776a.206.206,0,0,0-.12.037q-.216.15-.425.308a.207.207,0,1,0,.249.332q.2-.153.414-.3a.207.207,0,0,0-.117-.377Z" transform="translate(-1502.171 -44.843)" fill="#707070"/>
    <path id="Path_2041" data-name="Path 2041" d="M2040.644,521.572a.175.175,0,0,0-.015.2l1.1,1.817a9.258,9.258,0,0,0-.89,2.863l-1.936.873a.175.175,0,0,0-.1.175l.233,2.538a.175.175,0,0,0,.132.154l2.062.506a9.252,9.252,0,0,0,1.4,2.653l-.752,1.987a.174.174,0,0,0,.052.2l1.959,1.63a.175.175,0,0,0,.2.015l1.817-1.1a9.25,9.25,0,0,0,2.863.89l.873,1.936a.173.173,0,0,0,.175.1l2.538-.233a.175.175,0,0,0,.154-.132l.506-2.062a9.257,9.257,0,0,0,2.653-1.4l1.987.752a.174.174,0,0,0,.2-.052l1.63-1.959a.174.174,0,0,0,.015-.2l-1.1-1.817a9.262,9.262,0,0,0,.891-2.863l1.935-.873a.175.175,0,0,0,.1-.175l-.232-2.538a.175.175,0,0,0-.132-.154l-2.062-.506a9.257,9.257,0,0,0-1.4-2.653l.752-1.987a.174.174,0,0,0-.052-.2l-1.959-1.63a.174.174,0,0,0-.2-.015l-1.817,1.1a9.258,9.258,0,0,0-2.863-.891l-.872-1.935a.175.175,0,0,0-.176-.1l-2.537.233a.174.174,0,0,0-.154.132l-.506,2.062a9.253,9.253,0,0,0-2.653,1.4l-1.987-.752a.174.174,0,0,0-.2.052Z" transform="translate(-1463.543 -61.554)" fill="#f3f3f3" stroke="#707070" stroke-width="1"/>
    <path id="Path_2042" data-name="Path 2042" d="M2197.1,671.97a2.457,2.457,0,1,1-.365-.031A2.473,2.473,0,0,1,2197.1,671.97Zm.827,4.1a2.055,2.055,0,0,0-.892-3.692h0a2.055,2.055,0,1,0-.638,4.061,2.089,2.089,0,0,0,.3.025A2.04,2.04,0,0,0,2197.932,676.071Z" transform="translate(-1610.205 -208.216)" fill="#707070" opacity="0.56"/>
    <path id="Path_2043" data-name="Path 2043" d="M2132.626,603.879a6.462,6.462,0,0,0-.646.026h0l-.029,0a6.383,6.383,0,0,0-1.275.255l-.023.007a.208.208,0,0,0-.146.2.217.217,0,0,0,.01.064.207.207,0,0,0,.26.136,5.982,5.982,0,0,1,1.241-.248h0a5.943,5.943,0,0,1,6.455,5.375,5.858,5.858,0,0,1,.025.6,5.942,5.942,0,1,1-11.3-2.634.2.2,0,0,0,.02-.088.207.207,0,0,0-.394-.092,6.357,6.357,0,1,0,12.087,2.818q0-.321-.026-.647A6.366,6.366,0,0,0,2132.626,603.879Z" transform="translate(-1546.006 -144.004)" fill="#707070" opacity="0.56"/>
  </g>
</svg>
